// using System;
// using System.Threading.Tasks;
// using PetsBattleServerProto;

// namespace PetsServices.Battle
// {
//     #nullable enable
//     public class MinorActionParserOld: IBattleActionParser<IMinorActionExecutor>
//     {
//         // private readonly IBattleMainActionExecutor _mainExecutor;

//         // public IBattleActionExecutor Executor
//         // {
//         //     get
//         //     {
//         //         return Executor?;
//         //     }
//         // }
//         // public MinorActionParser(IBattleMainActionExecutor executor)
//         // {
//         //     _mainExecutor = executor;
//         // }
//        public IMinorActionExecutor? Executor { get; set; }
//         static public bool CanParser(BattleMessageUnit message)
//         {
//             string actionType = message.PickNextValue() ?? "";
//             if (Enum.TryParse(actionType.Replace("-", ""), true, out MinorActionType minortype))
//             {
//                 return true;
//             }
//             else
//             {
//                 return false;
//             }
//         }
//         public async Task<bool> Parse(BattleMessageUnit message)
//         {
//             string actionType = message.NextValue() ?? "";
//             bool isParse = true;
//             if (actionType.StartsWith("-") && Enum.TryParse(actionType.Replace("-", ""), true, out MinorActionType minortype))
//             {
//                 switch (minortype)
//                 {
//                     case MinorActionType.Fail:
//                         await ParseFailAction(message);
//                         break;
//                     case MinorActionType.Block:
//                         await ParseBlockAction(message);
//                         break;
//                     case MinorActionType.Notarget:
//                         await ParseNoTarget(message);
//                         break;
//                     case MinorActionType.Miss:
//                         await ParseMiss(message);
//                         break;
//                     case MinorActionType.Damage:
//                         await ParseDamage(message);
//                         break;
//                     case MinorActionType.Heal:
//                         await ParseHeal(message);
//                         break;
//                     case MinorActionType.Sethp:
//                         await ParseSetHp(message);
//                         break;
//                     case MinorActionType.Status:
//                         await ParseStatus(message);
//                         break;
//                     case MinorActionType.Curestatus:
//                         await ParseCureStatus(message);
//                         break;
//                     case MinorActionType.Cureteam:
//                         await ParseCureTeam(message);
//                         break;
//                     case MinorActionType.Boost:
//                         await ParseBoost(message);
//                         break;
//                     case MinorActionType.Unboost:
//                         await ParseUnboost(message);
//                         break;
//                     case MinorActionType.Setboost:
//                         await ParseSetBoost(message);
//                         break;
//                     case MinorActionType.Swapboost:
//                         await ParseSwapBoost(message);
//                         break;
//                     case MinorActionType.Invertboost:
//                         await ParseInvertBoost(message);
//                         break;
//                     case MinorActionType.Clearboost:
//                         await ParseClearBoost(message);
//                         break;
//                     case MinorActionType.Clearallboost:
//                         await ParseClearAllBoosts(message);
//                         break;
//                     case MinorActionType.Clearpositiveboost:
//                         await ParseClearPositiveBoost(message);
//                         break;
//                     case MinorActionType.Clearnegativeboost:
//                         await ParseClearNegativeBoost(message);
//                         break;
//                     case MinorActionType.Copyboost:
//                         await ParseCopyBoost(message);
//                         break;
//                     case MinorActionType.Weather:
//                         await ParseWeather(message);
//                         break;
//                     case MinorActionType.Fieldstart:
//                         await ParseFieldStart(message);
//                         break;
//                     case MinorActionType.Fieldactivate:
//                         await ParseFieldActivate(message);
//                         break;
//                     case MinorActionType.Fieldend:
//                         await ParseFieldEnd(message);
//                         break;
//                     case MinorActionType.Sidestart:
//                         await ParseSideStart(message);
//                         break;
//                     case MinorActionType.Sideend:
//                         await ParseSideEnd(message);
//                         break;
//                     case MinorActionType.Swapsideconditions:
//                         await ParseSwapSideConditions(message);
//                         break;
//                     case MinorActionType.Start:
//                         await ParseVolatileStart(message);
//                         break;
//                     case MinorActionType.End:
//                         await ParseVolatileEnd(message);
//                         break;
//                     case MinorActionType.Crit:
//                         await ParseCrit(message);
//                         break;
//                     case MinorActionType.Supereffective:
//                         await ParseSuperEffective(message);
//                         break;
//                     case MinorActionType.Resisted:
//                         await ParseResisted(message);
//                         break;
//                     case MinorActionType.Immune:
//                         await ParseImmune(message);
//                         break;
//                     case MinorActionType.Item:
//                         await ParseItem(message);
//                         break;
//                     case MinorActionType.Enditem:
//                         await ParseEndItem(message);
//                         break;
//                     case MinorActionType.Ability:
//                         await ParseAbility(message);
//                         break;
//                     case MinorActionType.Endability:
//                         await ParseEndAbility(message);
//                         break;
//                     case MinorActionType.Transform:
//                         await ParseTransform(message);
//                         break;
//                     case MinorActionType.Mega:
//                         await ParseMega(message);
//                         break;
//                     case MinorActionType.Primal:
//                         await ParsePrimal(message);
//                         break;
//                     case MinorActionType.Burst:
//                         await ParseBurst(message);
//                         break;
//                     case MinorActionType.Zpower:
//                         await ParseZPower(message);
//                         break;
//                     case MinorActionType.Zbroken:
//                         await ParseZBroken(message);
//                         break;
//                     case MinorActionType.Activate:
//                         await ParseActivate(message);
//                         break;
//                     case MinorActionType.Hint:
//                         await ParseHint(message);
//                         break;
//                     case MinorActionType.Center:
//                         await ParseCenter(message);
//                         break;
//                     case MinorActionType.Message:
//                         await ParseMessage(message);
//                         break;
//                     case MinorActionType.Combine:
//                         await ParseCombine(message);
//                         break;
//                     case MinorActionType.Waiting:
//                         await ParseWaiting(message);
//                         break;
//                     case MinorActionType.Prepare:
//                         await ParsePrepare(message);
//                         break;
//                     case MinorActionType.Mustrecharge:
//                         await ParseMustRecharge(message);
//                         break;
//                     case MinorActionType.Nothing:
//                         await ParseNothing(message);
//                         break;
//                     case MinorActionType.Hitcount:
//                         await ParseHitCount(message);
//                         break;
//                     case MinorActionType.Singlemove:
//                         await ParseSingleMove(message);
//                         break;
//                     case MinorActionType.Singleturn:
//                         await ParseSingleTurn(message);
//                         break;
//                     default:
//                         PLog.Warn($"Unknown minor action: {actionType}");
//                         isParse = false;
//                         break;
//                         // _mainExecutor.ShowMessage($"Unknown minor action: {actionType}");
//                         // break;
//                 }
//             }
//             else
//             {
//                 PLog.Warn($"Invalid action type: {actionType}");
//                 isParse = false;
//                 // _mainExecutor.ShowMessage($"Invalid action type: {actionType}");
//             }
//             if(!isParse) {
//                 message.ResetMessage();
//             }
//             return isParse;
//         }

//         private async Task ParseFailAction(BattleMessageUnit message)
//         {
//             string? pokemon = message.NextValue();
//             string? action = message.NextValue();
//             if(Executor != null) {
//                 await Executor.FailAction(pokemon!, action!);
//             }
//         }

//         private async Task ParseBlockAction(BattleMessageUnit message)
//         {
//             string? pokemon = message.NextValue();
//             string? effect = message.NextValue();
//             string? move = message.NextValue();
//             string? attacker = message.NextValue();
//             if(Executor != null) {
//               await Executor.BlockAction(pokemon!, effect!, move, attacker);
//             }
//         }

//         private async Task ParseNoTarget(BattleMessageUnit message)
//         {
//             string? pokemon = message.NextValue();
//             if(Executor != null) {
//                 await Executor.NoTarget(pokemon!);
//             }
//         }

//         private async Task ParseMiss(BattleMessageUnit message)
//         {
//             string? source = message.NextValue();
//             string? target = message.NextValue();
//             if(Executor != null) {
//                 await Executor.MoveMissed(source!, target!);
//             }
//         }

//         private async Task ParseDamage(BattleMessageUnit message)
//         {
//             string? pokemon = message.NextValue();
//             string? hpStatus = message.NextValue();
//             if(Executor != null) {
//                 await Executor.DamagePokemon(pokemon!, hpStatus!);
//             }
//         }

//         private async Task ParseHeal(BattleMessageUnit message)
//         {
//             string? pokemon = message.NextValue();
//             string? hpStatus = message.NextValue();
//             if(Executor != null) {
//                 await Executor.HealPokemon(pokemon!, hpStatus!);
//             }
//         }

//         private async Task ParseSetHp(BattleMessageUnit message)
//         {
//             string? pokemon = message.NextValue();
//             string? hp = message.NextValue();
//             if(Executor != null) {
//                 await Executor.SetHp(pokemon!, hp!);
//             }
//         }

//         private async Task ParseStatus(BattleMessageUnit message)
//         {
//             string? pokemon = message.NextValue();
//             string? status = message.NextValue();
//             if(Executor != null) {
//                 await Executor.InflictStatus(pokemon!, status!);
//             }
//         }

//         private async Task ParseCureStatus(BattleMessageUnit message)
//         {
//             string? pokemon = message.NextValue();
//             string? status = message.NextValue();
//             if(Executor != null) {
//                 await Executor.CureStatus(pokemon!, status!);
//             }
//         }

//         private async Task ParseCureTeam(BattleMessageUnit message)
//         {
//             string? pokemon = message.NextValue();
//             if(Executor != null) {
//                 await Executor.CureTeam(pokemon!);
//             }
//         }

//         private async Task ParseBoost(BattleMessageUnit message)
//         {
//             string? pokemon = message.NextValue();
//             string? stat = message.NextValue();
//             string? amountStr = message.NextValue();
//             if (int.TryParse(amountStr, out int amount) && Executor != null)
//             {
//                 await Executor.BoostStat(pokemon!, stat!, amount);
//             }
//         }

//         private async Task ParseUnboost(BattleMessageUnit message)
//         {
//             string? pokemon = message.NextValue();
//             string? stat = message.NextValue();
//             string? amountStr = message.NextValue();
//             if (int.TryParse(amountStr, out int amount) && Executor != null)
//             {
//                 await Executor.UnboostStat(pokemon!, stat!, amount);
//             }
//         }

//         private async Task ParseSetBoost(BattleMessageUnit message)
//         {
//             string? pokemon = message.NextValue();
//             string? stat = message.NextValue();
//             string? amountStr = message.NextValue();
//             if (int.TryParse(amountStr, out int amount) && Executor != null)
//             {
//                 await Executor.SetBoost(pokemon!, stat!, amount);
//             }
//         }

//         private async Task ParseSwapBoost(BattleMessageUnit message)
//         {
//             string? source = message.NextValue();
//             string? target = message.NextValue();
//             string? stats = message.NextValue();
//             if(Executor != null) {
//                 await Executor.SwapBoost(source!, target!, stats!);
//             }
//         }

//         private async Task ParseInvertBoost(BattleMessageUnit message)
//         {
//             string? pokemon = message.NextValue();
//             if(Executor != null) {
//                 await Executor.InvertBoost(pokemon!);
//             }
//         }

//         private async Task ParseClearBoost(BattleMessageUnit message)
//         {
//             string? pokemon = message.NextValue();
//             if(Executor != null) {
//                 await Executor.ClearBoost(pokemon!);
//             }
//         }

//         private async Task ParseClearAllBoosts(BattleMessageUnit message)
//         {
//             if(Executor != null) {
//                 await Executor.ClearAllBoosts();
//             }
//         }

//         private async Task ParseClearPositiveBoost(BattleMessageUnit message)
//         {
//             string? target = message.NextValue();
//             string? pokemon = message.NextValue();
//             string? effect = message.NextValue();
//             if(Executor != null) {
//                 await Executor.ClearPositiveBoost(target!, pokemon!, effect!);
//             }
//         }

//         private async Task ParseClearNegativeBoost(BattleMessageUnit message)
//         {
//             string? pokemon = message.NextValue();
//             if(Executor != null) {
//                 await Executor.ClearNegativeBoost(pokemon!);
//             }
//         }

//         private async Task ParseCopyBoost(BattleMessageUnit message)
//         {
//             string? source = message.NextValue();
//             string? target = message.NextValue();
//             if(Executor != null) {
//                 await Executor.CopyBoost(source!, target!);
//             }
//         }

//         private async Task ParseWeather(BattleMessageUnit message)
//         {
//             string? weather = message.NextValue();
//             bool upkeep = message.ContainsTag("[upkeep]");
//             if(Executor != null) {
//                 await Executor.ChangeWeather(weather!, upkeep);
//             }
//         }

//         private async Task ParseFieldActivate(BattleMessageUnit message)
//         {
//             string? condition = message.NextValue();
//             if(Executor != null) {
//                 await Executor.ActiveFieldCondition(condition!);
//             }
//         }
//         private async Task ParseFieldStart(BattleMessageUnit message)
//         {
//             string? condition = message.NextValue();
//             if(Executor != null) {
//                 await Executor.StartFieldCondition(condition!);
//             }
//         }
//         private async Task ParseFieldEnd(BattleMessageUnit message)
//         {
//             string? condition = message.NextValue();
//             if(Executor != null) {
//                 await Executor.EndFieldCondition(condition!);
//             }
//         }

//         private async Task ParseSideStart(BattleMessageUnit message)
//         {
//             string? side = message.NextValue();
//             string? condition = message.NextValue();
//             if(Executor != null) {
//                 await Executor.StartSideCondition(side!, condition!);
//             }
//         }

//         private async Task ParseSideEnd(BattleMessageUnit message)
//         {
//             string? side = message.NextValue();
//             string? condition = message.NextValue();
//             if(Executor != null) {
//                 await Executor.EndSideCondition(side!, condition!);
//             }
//         }

//         private async Task ParseSwapSideConditions(BattleMessageUnit message)
//         {
//             if(Executor != null) {
//                 await Executor.SwapSideConditions();
//             }
//         }

//         private async Task ParseVolatileStart(BattleMessageUnit message)
//         {
//             string? pokemon = message.NextValue();
//             string? effect = message.NextValue();
//             string? ext = message.NextValue();
//             // |-start|p1a: Falinks|Disable|Throat Chop|[from] ability: Cursed Body|[of] p2a: Gengar
//             // |-start|p1a: kyogre|Dynamax|
//             if(Executor != null && !string.IsNullOrEmpty(effect)) {
//                 switch (effect) {
//                     case "Dynamax":
//                         // await Executor.ApplyVolatileStatus(pokemon!, effect!, message.From, message.Of, ext);
//                         await Executor.Transform(pokemon!, effect!);
//                         break;
//                     default:
//                         await Executor.ApplyVolatileStatus(pokemon!, effect!, message.From, message.Of, ext);
//                         break;
//                 }
//                 // await Executor.ApplyVolatileStatus(pokemon!, effect!, message.From, message.Of, ext);
//             }
//         }

//         private async Task ParseVolatileEnd(BattleMessageUnit message)
//         {
//             string? pokemon = message.NextValue();
//             string? effect = message.NextValue();
//             if(Executor != null) {
//                 await Executor.RemoveVolatileStatus(pokemon!, effect!);
//             }
//         }

//         private async Task ParseCrit(BattleMessageUnit message)
//         {
//             string? pokemon = message.NextValue();
//             if(Executor != null) {
//                 await Executor.CriticalHit(pokemon!);
//             }
//         }

//         private async Task ParseSuperEffective(BattleMessageUnit message)
//         {
//             string? pokemon = message.NextValue();
//             if(Executor != null) {
//                 await Executor.SuperEffective(pokemon!);
//             }
//         }
//         private async Task ParseResisted(BattleMessageUnit message)
//         {
//             string? pokemon = message.NextValue();
//             if(Executor != null) {
//                 await Executor.MoveResisted(pokemon!);
//             }
//         }

//         private async Task ParseImmune(BattleMessageUnit message)
//         {
//             string? pokemon = message.NextValue();
//             if(Executor != null) {
//                 await Executor.MoveImmune(pokemon!);
//             }
//         }

//         private async Task ParseItem(BattleMessageUnit message)
//         {
//             string? pokemon = message.NextValue();
//             string? item = message.NextValue();
//             string? effect = message.NextValue();
//             if(Executor != null) {
//                 if (effect != null && effect.StartsWith("[from]"))
//                 {
//                     effect = effect.Replace("[from]", "").Trim();
//                     await Executor.ItemChanged(pokemon!, item!, effect);
//                 }
//                 else
//                 {
//                     await Executor.ItemRevealed(pokemon!, item!);
//                 }
//             }
//         }

//         private async Task ParseEndItem(BattleMessageUnit message)
//         {
//             string? pokemon = message.NextValue();
//             string? item = message.NextValue();
//             string? effect = message.NextValue();
//             if(Executor != null) {
//                 if (effect != null && effect.StartsWith("[from]"))
//                 {
//                     effect = effect.Replace("[from]", "").Trim();
//                     await Executor.ItemDestroyed(pokemon!, item!, effect);
//                 }
//                 else
//                 {
//                     await Executor.ItemConsumed(pokemon!, item!);
//                 }
//             }
//         }

//         private async Task ParseAbility(BattleMessageUnit message)
//         {
//             string? pokemon = message.NextValue();
//             string? ability = message.NextValue();
//             string? effect = message.NextValue();
//             if(Executor != null) {
//             if (effect != null && effect.StartsWith("[from]"))
//             {
//                 effect = effect.Replace("[from]", "").Trim();
//                 await Executor.Ability(pokemon!, ability!, effect);
//             }
//             else
//             {
//                 await Executor.AbilityRevealed(pokemon!, ability!);
//             }
//             }
//         }

//         private async Task ParseEndAbility(BattleMessageUnit message)
//         {
//             string? pokemon = message.NextValue();
//             if(Executor != null) {
//                 await Executor.AbilitySuppressed(pokemon!);
//             }
//         }

//         private async Task ParseTransform(BattleMessageUnit message)
//         {
//             string? pokemon = message.NextValue();
//             string? species = message.NextValue();
//             if(Executor != null) {
//                 await Executor.Transform(pokemon!, species!);
//             }
//         }

//         private async Task ParseMega(BattleMessageUnit message)
//         {
//             string? pokemon = message.NextValue();
//             string? megaStone = message.NextValue();
//             if(Executor != null) {
//                 await Executor.MegaEvolve(pokemon!, megaStone!);
//             }
//         }

//         private async Task ParsePrimal(BattleMessageUnit message)
//         {
//             string? pokemon = message.NextValue();
//             if(Executor != null) {
//                 await Executor.PrimalRevert(pokemon!);
//             }
//         }

//         private async Task ParseBurst(BattleMessageUnit message)
//         {
//             string? pokemon = message.NextValue();
//             string? species = message.NextValue();
//             string? item = message.NextValue();
//             if(Executor != null) {
//                 await Executor.UltraBurst(pokemon!, species!, item!);
//             }
//         }

//         private async Task ParseZPower(BattleMessageUnit message)
//         {
//             string? pokemon = message.NextValue();
//             if(Executor != null) {
//                 await Executor.UseZPower(pokemon!);
//             }
//         }

//         private async Task ParseZBroken(BattleMessageUnit message)
//         {
//             string? pokemon = message.NextValue();
//             if(Executor != null) {
//                 await Executor.ZMoveBreakThrough(pokemon!);
//             }
//         }

//         private async Task ParseActivate(BattleMessageUnit message)
//         {
//             string? effect = message.NextValue();
//             if(Executor != null) {
//                 await Executor.ActivateEffect(effect!);
//             }
//         }

//         private async Task ParseHint(BattleMessageUnit message)
//         {
//             string? hintMessage = message.NextValue();
//             if(Executor != null) {
//                 await Executor.ShowHint(hintMessage!);
//             }
//         }

//         private async Task ParseCenter(BattleMessageUnit message)
//         {
//             if(Executor != null) {
//                 await Executor.CenterPokemon();
//             }
//         }

//         private async Task ParseMessage(BattleMessageUnit message)
//         {
//             string? customMessage = message.NextValue();
//             if(Executor != null) {
//                 await Executor.ShowCustomMessage(customMessage!);
//             }
//         }

//         private async Task ParseCombine(BattleMessageUnit message)
//         {
//             if(Executor != null) {
//                 await Executor.CombineMove();
//             }
//         }

//         private async Task ParseWaiting(BattleMessageUnit message)
//         {
//             string? source = message.NextValue();
//             string? target = message.NextValue();
//             if(Executor != null) {
//                 await Executor.WaitingForTarget(source!, target!);
//             }
//         }

//         private async Task ParsePrepare(BattleMessageUnit message)
//         {
//             string? attacker = message.NextValue();
//             string? move = message.NextValue();
//             string? defender = message.NextValue();
//             if(Executor != null) {
//                 if (defender != null)
//                 {
//                     await Executor.PrepareMove(attacker!, move!, defender);
//                 }
//                 else
//                 {
//                     await Executor.PrepareMove(attacker!, move!);
//                 }
//             }
//         }

//         private async Task ParseMustRecharge(BattleMessageUnit message)
//         {
//             string? pokemon = message.NextValue();
//             if(Executor != null) {
//                 await Executor.MustRecharge(pokemon!);
//             }
//         }

//         private async Task ParseNothing(BattleMessageUnit message)
//         {
//             if(Executor != null) {
//                 await Executor.MoveDidNothing();
//             }
//         }

//         private async Task ParseHitCount(BattleMessageUnit message)
//         {
//             string? pokemon = message.NextValue();
//             string? numHitsStr = message.NextValue();
//             if (int.TryParse(numHitsStr, out int numHits) && Executor != null)
//             {
//                 await Executor.MultiHitMove(pokemon!, numHits);
//             }
//         }

//         private async Task ParseSingleMove(BattleMessageUnit message)
//         {
//             string? pokemon = message.NextValue();
//             string? move = message.NextValue();
//             if(Executor != null) {
//                 await Executor.SingleMoveEffect(pokemon!, move!);
//             }
//         }

//         private async Task ParseSingleTurn(BattleMessageUnit message)
//         {
//             string? pokemon = message.NextValue();
//             string? move = message.NextValue();
//             if(Executor != null) {
//                 await Executor.SingleTurnEffect(pokemon!, move!);
//             }
//         }
//     }
// }
