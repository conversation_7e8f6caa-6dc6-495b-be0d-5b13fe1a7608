using System;
using System.Threading.Tasks;
using PetsBattleServerProto;

namespace PetsServices.Battle
{
#nullable enable
    public class MinorActionParser : IBattleActionParser<IMinorActionExecutor>
    {
        // private readonly IBattleMainActionExecutor _mainExecutor;

        // public IBattleActionExecutor Executor
        // {
        //     get
        //     {
        //         return Executor?;
        //     }
        // }
        // public MinorActionParser(IBattleMainActionExecutor executor)
        // {
        //     _mainExecutor = executor;
        // }
        // public int 
        public IMinorActionExecutor? Executor { get; set; }
        static public bool CanParser(BattleMessageUnit message)
        {
            string actionType = message.PickNextValue() ?? "";
            switch (actionType)
            {
                case "-damage":
                    break;
                case "-heal":
                    break;
                case "-sethp":
                    break;
                case "-boost":
                    break;
                case "-unboost":
                    break;
                case "-setboost":
                    break;
                case "-swapboost":
                    break;
                case "-clearpositiveboost":
                    break;
                case "-clearnegativeboost":
                    break;
                case "-copyboost":
                    break;
                case "-clearboost":
                    break;
                case "-invertboost":
                    break;
                case "-clearallboost":
                    break;
                case "-crit":
                    break;
                case "-supereffective":
                    break;
                case "-resisted":
                    break;
                case "-immune":
                    break;
                case "-miss":
                    break;
                case "-fail":
                    break;
                case "-block":
                    break;
                case "-center":
                    break;
                case "-notarget":
                    break;
                case "-ohko":
                    break;
                case "-combine":
                    break;
                case "-hitcount":
                    break;
                case "-waiting":
                    break;
                case "-zbroken":
                    break;
                case "-zpower":
                    break;
                case "-prepare":
                    break;
                case "-mustrecharge":
                    break;
                case "-status":
                    break;
                case "-curestatus":
                    break;
                case "-cureteam":
                    break;
                case "-item":
                    break;
                case "-enditem":
                    break;
                case "-ability":
                    break;
                case "-endability":
                    break;
                case "detailschange":
                    break;
                case "-transform":
                    break;
                case "-formechange":
                    break;
                case "-mega":
                    break;
                case "-primal":
                    break;
                case "-burst":
                    break;
                case "-terastallize":
                    break;
                case "-start":
                    break;
                case "-end":
                    break;
                case "-singleturn":
                    break;
                case "-singlemove":
                    break;
                case "-activate":
                    break;
                case "-sidestart":
                    break;
                case "-sideend":
                    break;
                case "-swapsideconditions":
                    break;
                case "-weather":
                    break;
                case "-fieldstart":
                    break;
                case "-fieldend":
                    break;
                case "-fieldactivate":
                    break;
                case "-anim":
                    break;
                case "-hint":
                    break;
                case "-message":
                    break;
                case "-candynamax":
                    break;
                default:
                    PLog.Warn($"Unknown minor action: {actionType}");
                    return false;
            }
            return true;
            // if (Enum.TryParse(actionType.Replace("-", ""), true, out MinorActionType minortype))
            // {
            //     return true;
            // }
            // else
            // {
            //     return false;
            // }
        }
        public async Task<bool> Parse(BattleMessageUnit message)
        {
            string actionType = message.NextValue() ?? "";
            bool isParse = false;
            switch (actionType)
            {
                case "-damage":
                    if (Executor != null) { isParse = true; await Executor.Damage(message); }
                    break;
                case "-heal":
                    if (Executor != null) { isParse = true; await Executor.Heal(message); }
                    break;
                case "-sethp":
                    if (Executor != null) { isParse = true; await Executor.Sethp(message); }
                    break;
                case "-boost":
                    if (Executor != null) { isParse = true; await Executor.Boost(message); }
                    break;
                case "-unboost":
                    if (Executor != null) { isParse = true; await Executor.Unboost(message); }
                    break;
                case "-setboost":
                    if (Executor != null) { isParse = true; await Executor.Setboost(message); }
                    break;
                case "-swapboost":
                    if (Executor != null) { isParse = true; await Executor.Swapboost(message); }
                    break;
                case "-clearpositiveboost":
                    if (Executor != null) { isParse = true; await Executor.Clearpositiveboost(message); }
                    break;
                case "-clearnegativeboost":
                    if (Executor != null) { isParse = true; await Executor.Clearnegativeboost(message); }
                    break;
                case "-copyboost":
                    if (Executor != null) { isParse = true; await Executor.Copyboost(message); }
                    break;
                case "-clearboost":
                    if (Executor != null) { isParse = true; await Executor.Clearboost(message); }
                    break;
                case "-invertboost":
                    if (Executor != null) { isParse = true; await Executor.Invertboost(message); }
                    break;
                case "-clearallboost":
                    if (Executor != null) { isParse = true; await Executor.Clearallboost(message); }
                    break;
                case "-crit":
                    if (Executor != null) { isParse = true; await Executor.Crit(message); }
                    break;
                case "-supereffective":
                    if (Executor != null) { isParse = true; await Executor.Supereffective(message); }
                    break;
                case "-resisted":
                    if (Executor != null) { isParse = true; await Executor.Resisted(message); }
                    break;
                case "-immune":
                    if (Executor != null) { isParse = true; await Executor.Immune(message); }
                    break;
                case "-miss":
                    if (Executor != null) { isParse = true; await Executor.Miss(message); }
                    break;
                case "-fail":
                    if (Executor != null) { isParse = true; await Executor.Fail(message); }
                    break;
                case "-block":
                    if (Executor != null) { isParse = true; await Executor.Block(message); }
                    break;
                case "-center":
                    if (Executor != null) { isParse = true; await Executor.Center(message); }
                    break;
                case "-notarget":
                    if (Executor != null) { isParse = true; await Executor.Notarget(message); }
                    break;
                case "-ohko":
                    if (Executor != null) { isParse = true; await Executor.Ohko(message); }
                    break;
                case "-combine":
                    if (Executor != null) { isParse = true; await Executor.Combine(message); }
                    break;
                case "-hitcount":
                    if (Executor != null) { isParse = true; await Executor.Hitcount(message); }
                    break;
                case "-waiting":
                    if (Executor != null) { isParse = true; await Executor.Waiting(message); }
                    break;
                case "-zbroken":
                    if (Executor != null) { isParse = true; await Executor.Zbroken(message); }
                    break;
                case "-zpower":
                    if (Executor != null) { isParse = true; await Executor.Zpower(message); }
                    break;
                case "-prepare":
                    if (Executor != null) { isParse = true; await Executor.Prepare(message); }
                    break;
                case "-mustrecharge":
                    if (Executor != null) { isParse = true; await Executor.Mustrecharge(message); }
                    break;
                case "-status":
                    if (Executor != null) { isParse = true; await Executor.Status(message); }
                    break;
                case "-curestatus":
                    if (Executor != null) { isParse = true; await Executor.Curestatus(message); }
                    break;
                case "-cureteam":
                    if (Executor != null) { isParse = true; await Executor.Cureteam(message); }
                    break;
                case "-item":
                    if (Executor != null) { isParse = true; await Executor.Item(message); }
                    break;
                case "-enditem":
                    if (Executor != null) { isParse = true; await Executor.Enditem(message); }
                    break;
                case "-ability":
                    if (Executor != null) { isParse = true; await Executor.Ability(message); }
                    break;
                case "-endability":
                    if (Executor != null) { isParse = true; await Executor.Endability(message); }
                    break;
                case "detailschange":
                    if (Executor != null) { isParse = true; await Executor.Detailschange(message); }
                    break;
                case "-transform":
                    if (Executor != null) { isParse = true; await Executor.Transform(message); }
                    break;
                case "-formechange":
                    if (Executor != null) { isParse = true; await Executor.Formechange(message); }
                    break;
                case "-mega":
                    if (Executor != null) { isParse = true; await Executor.Mega(message); }
                    break;
                case "-primal":
                    if (Executor != null) { isParse = true; await Executor.Primal(message); }
                    break;
                case "-burst":
                    if (Executor != null) { isParse = true; await Executor.Burst(message); }
                    break;
                case "-terastallize":
                    if (Executor != null) { isParse = true; await Executor.Terastallize(message); }
                    break;
                case "-start":
                    if (Executor != null) { isParse = true; await Executor.Start(message); }
                    break;
                case "-end":
                    if (Executor != null) { isParse = true; await Executor.End(message); }
                    break;
                case "-singleturn":
                    if (Executor != null) { isParse = true; await Executor.Singleturn(message); }
                    break;
                case "-singlemove":
                    if (Executor != null) { isParse = true; await Executor.Singlemove(message); }
                    break;
                case "-activate":
                    if (Executor != null) { isParse = true; await Executor.Activate(message); }
                    break;
                case "-sidestart":
                    if (Executor != null) { isParse = true; await Executor.Sidestart(message); }
                    break;
                case "-sideend":
                    if (Executor != null) { isParse = true; await Executor.Sideend(message); }
                    break;
                case "-swapsideconditions":
                    if (Executor != null) { isParse = true; await Executor.Swapsideconditions(message); }
                    break;
                case "-weather":
                    if (Executor != null) { isParse = true; await Executor.Weather(message); }
                    break;
                case "-fieldstart":
                    if (Executor != null) { isParse = true; await Executor.Fieldstart(message); }
                    break;
                case "-fieldend":
                    if (Executor != null) { isParse = true; await Executor.Fieldend(message); }
                    break;
                case "-fieldactivate":
                    if (Executor != null) { isParse = true; await Executor.Fieldactivate(message); }
                    break;
                case "-anim":
                    if (Executor != null) { isParse = true; await Executor.Anim(message); }
                    break;
                case "-hint":
                    if (Executor != null) { isParse = true; await Executor.Hint(message); }
                    break;
                case "-message":
                    if (Executor != null) { isParse = true; await Executor.Message(message); }
                    break;
                case "-candynamax":
                    if (Executor != null) { isParse = true; await Executor.Candynamax(message); }
                    break;
                default:
                    PLog.Warn($"Unknown minor action: {actionType}");
                    break;
            }
            //     switch (actionType) {
            // case "-damage": {
            //     if(Executor != null) {
            //         isParse = true;
            //         await Executor.Damage(message);
            //     }
            //     // string? pokemon = message.NextValue();
            //     // string? hpStatus = message.NextValue();
            //     // if(string.IsNullOrEmpty(pokemon) || string.IsNullOrEmpty(hpStatus)) {
            //     //     break;
            //     // }
            //     // if(Executor != null) {
            //     //     isParse = true;
            //     //     await Executor.DamagePokemon(pokemon!, hpStatus!, message.From);
            //     // }
            // 	break;
            // }
            // case "-heal": {
            //     string? pokemon = message.NextValue();
            //     string? hpStatus = message.NextValue();
            //     if(string.IsNullOrEmpty(pokemon) || string.IsNullOrEmpty(hpStatus)) {
            //         break;
            //     }
            //     if(Executor != null) {
            //         isParse = true;
            //         await Executor.HealPokemon(pokemon!, hpStatus!, message.From);
            //     }
            // 	break;
            // }
            // case "-sethp": {
            //     string? pokemon = message.NextValue();
            //     string? hp = message.NextValue();
            //     if(string.IsNullOrEmpty(pokemon) || string.IsNullOrEmpty(hp)) {
            //         break;
            //     }
            //     if(Executor != null) {
            //         isParse = true;
            //         await Executor.SetHp(pokemon!, hp!);
            //     }
            // 	break;
            // }
            // case "-boost": {
            //     string? pokemon = message.NextValue();
            //     string? stat = message.NextValue();
            //     string? amountStr = message.NextValue();
            //     if(string.IsNullOrEmpty(pokemon) || string.IsNullOrEmpty(stat) || string.IsNullOrEmpty(amountStr)) {
            //         break;
            //     }
            //     if (int.TryParse(amountStr, out int amount) && Executor != null)
            //     {
            //         isParse = true;
            //         await Executor.BoostStat(pokemon!, stat!, amount);
            //     }
            // 	break;
            // }
            // case "-unboost": {
            //     string? pokemon = message.NextValue();
            //     string? stat = message.NextValue();
            //     string? amountStr = message.NextValue();
            //     if(string.IsNullOrEmpty(pokemon) || string.IsNullOrEmpty(stat) || string.IsNullOrEmpty(amountStr)) {
            //         break;
            //     }
            //     if (int.TryParse(amountStr, out int amount) && Executor != null)
            //     {
            //         isParse = true;
            //         await Executor.UnboostStat(pokemon!, stat!, amount);
            //     }
            // 	break;
            // }
            // case "-setboost": {
            //     string? pokemon = message.NextValue();
            //     string? stat = message.NextValue();
            //     string? amountStr = message.NextValue();
            // 	break;
            // }
            // case "-swapboost": {
            // 	break;
            // }
            // case "-clearpositiveboost": {
            // 	break;
            // }
            // case "-clearnegativeboost": {
            // 	break;
            // }
            // case "-copyboost": {
            // 	break;
            // }
            // case "-clearboost": {
            // 	break;
            // }
            // case "-invertboost": {
            // 	break;
            // }
            // case "-clearallboost": {
            // 	break;
            // }
            // case "-crit": {
            // 	break;
            // }
            // case "-supereffective": {
            // 	break;
            // }
            // case "-resisted": {
            // 	break;
            // }
            // case "-immune": {
            // 	break;
            // }
            // case "-miss": {
            // 	break;
            // }
            // case "-fail": {

            // 	break;
            // }
            // case "-block": {

            // 	break;
            // }
            // case "-center": case "-notarget": case "-ohko":
            // case "-combine": case "-hitcount": case "-waiting": case "-zbroken": {
            // 	// this.log(args, kwArgs);
            // 	break;
            // }
            // case "-zpower": {
            // 	break;
            // }
            // case "-prepare": {
            // 	break;
            // }
            // case "-mustrecharge": {
            // 	break;
            // }
            // case "-status": {

            // 	break;
            // }
            // case "-curestatus": {

            // 	break;

            // }
            // case "-cureteam": { // For old gens when the whole team was always cured

            // 	break;
            // }
            // case "-item": {

            // 	break;
            // }
            // case "-enditem": {

            // 	break;
            // }
            // case "-ability": {

            // 	break;
            // }
            // case "-endability": {

            // 	break;
            // }
            // case "detailschange": {

            // 	break;
            // }
            // case "-transform": {

            // 	break;
            // }
            // case "-formechange": {

            // 	break;
            // }
            // case "-mega": {

            // 	break;
            // }
            // case "-primal": case "-burst": {
            // 	break;
            // }
            // case "-terastallize": {

            // 	break;
            // }
            // case "-start": {

            // 	break;
            // }
            // case "-end": {

            // 	break;
            // }
            // case "-singleturn": {

            // 	break;
            // }
            // case "-singlemove": {

            // 	break;
            // }
            // case "-activate": {

            // 	break;
            // }
            // case "-sidestart": {

            // 	break;
            // }
            // case "-sideend": {

            // 	break;
            // }
            // case "-swapsideconditions": {
            // 	break;
            // }
            // case "-weather": {
            // 	break;
            // }
            // case "-fieldstart": {

            // 	break;
            // }
            // case "-fieldend": {

            // 	break;
            // }
            // case "-fieldactivate": {
            // 	break;
            // }
            // case "-anim": {

            // 	break;
            // }
            // case "-hint": case "-message": case "-candynamax": {

            // 	break;
            // }
            // default: {
            // 	// throw new Error(`Unrecognized minor action: ${args[0]}`);
            // 	break;
            // }}
            // if (actionType.StartsWith("-") && Enum.TryParse(actionType.Replace("-", ""), true, out MinorActionType minortype))
            // {
            //     switch (minortype)
            //     {
            //         case MinorActionType.Fail:
            //             await ParseFailAction(message);
            //             break;
            //         case MinorActionType.Block:
            //             await ParseBlockAction(message);
            //             break;
            //         case MinorActionType.Notarget:
            //             await ParseNoTarget(message);
            //             break;
            //         case MinorActionType.Miss:
            //             await ParseMiss(message);
            //             break;
            //         case MinorActionType.Damage:
            //             await ParseDamage(message);
            //             break;
            //         case MinorActionType.Heal:
            //             await ParseHeal(message);
            //             break;
            //         case MinorActionType.Sethp:
            //             await ParseSetHp(message);
            //             break;
            //         case MinorActionType.Status:
            //             await ParseStatus(message);
            //             break;
            //         case MinorActionType.Curestatus:
            //             await ParseCureStatus(message);
            //             break;
            //         case MinorActionType.Cureteam:
            //             await ParseCureTeam(message);
            //             break;
            //         case MinorActionType.Boost:
            //             await ParseBoost(message);
            //             break;
            //         case MinorActionType.Unboost:
            //             await ParseUnboost(message);
            //             break;
            //         case MinorActionType.Setboost:
            //             await ParseSetBoost(message);
            //             break;
            //         case MinorActionType.Swapboost:
            //             await ParseSwapBoost(message);
            //             break;
            //         case MinorActionType.Invertboost:
            //             await ParseInvertBoost(message);
            //             break;
            //         case MinorActionType.Clearboost:
            //             await ParseClearBoost(message);
            //             break;
            //         case MinorActionType.Clearallboost:
            //             await ParseClearAllBoosts(message);
            //             break;
            //         case MinorActionType.Clearpositiveboost:
            //             await ParseClearPositiveBoost(message);
            //             break;
            //         case MinorActionType.Clearnegativeboost:
            //             await ParseClearNegativeBoost(message);
            //             break;
            //         case MinorActionType.Copyboost:
            //             await ParseCopyBoost(message);
            //             break;
            //         case MinorActionType.Weather:
            //             await ParseWeather(message);
            //             break;
            //         case MinorActionType.Fieldstart:
            //             await ParseFieldStart(message);
            //             break;
            //         case MinorActionType.Fieldactivate:
            //             await ParseFieldActivate(message);
            //             break;
            //         case MinorActionType.Fieldend:
            //             await ParseFieldEnd(message);
            //             break;
            //         case MinorActionType.Sidestart:
            //             await ParseSideStart(message);
            //             break;
            //         case MinorActionType.Sideend:
            //             await ParseSideEnd(message);
            //             break;
            //         case MinorActionType.Swapsideconditions:
            //             await ParseSwapSideConditions(message);
            //             break;
            //         case MinorActionType.Start:
            //             await ParseVolatileStart(message);
            //             break;
            //         case MinorActionType.End:
            //             await ParseVolatileEnd(message);
            //             break;
            //         case MinorActionType.Crit:
            //             await ParseCrit(message);
            //             break;
            //         case MinorActionType.Supereffective:
            //             await ParseSuperEffective(message);
            //             break;
            //         case MinorActionType.Resisted:
            //             await ParseResisted(message);
            //             break;
            //         case MinorActionType.Immune:
            //             await ParseImmune(message);
            //             break;
            //         case MinorActionType.Item:
            //             await ParseItem(message);
            //             break;
            //         case MinorActionType.Enditem:
            //             await ParseEndItem(message);
            //             break;
            //         case MinorActionType.Ability:
            //             await ParseAbility(message);
            //             break;
            //         case MinorActionType.Endability:
            //             await ParseEndAbility(message);
            //             break;
            //         case MinorActionType.Transform:
            //             await ParseTransform(message);
            //             break;
            //         case MinorActionType.Mega:
            //             await ParseMega(message);
            //             break;
            //         case MinorActionType.Primal:
            //             await ParsePrimal(message);
            //             break;
            //         case MinorActionType.Burst:
            //             await ParseBurst(message);
            //             break;
            //         case MinorActionType.Zpower:
            //             await ParseZPower(message);
            //             break;
            //         case MinorActionType.Zbroken:
            //             await ParseZBroken(message);
            //             break;
            //         case MinorActionType.Activate:
            //             await ParseActivate(message);
            //             break;
            //         case MinorActionType.Hint:
            //             await ParseHint(message);
            //             break;
            //         case MinorActionType.Center:
            //             await ParseCenter(message);
            //             break;
            //         case MinorActionType.Message:
            //             await ParseMessage(message);
            //             break;
            //         case MinorActionType.Combine:
            //             await ParseCombine(message);
            //             break;
            //         case MinorActionType.Waiting:
            //             await ParseWaiting(message);
            //             break;
            //         case MinorActionType.Prepare:
            //             await ParsePrepare(message);
            //             break;
            //         case MinorActionType.Mustrecharge:
            //             await ParseMustRecharge(message);
            //             break;
            //         case MinorActionType.Nothing:
            //             await ParseNothing(message);
            //             break;
            //         case MinorActionType.Hitcount:
            //             await ParseHitCount(message);
            //             break;
            //         case MinorActionType.Singlemove:
            //             await ParseSingleMove(message);
            //             break;
            //         case MinorActionType.Singleturn:
            //             await ParseSingleTurn(message);
            //             break;
            //         default:
            //             PLog.Warn($"Unknown minor action: {actionType}");
            //             isParse = false;
            //             break;
            //             // _mainExecutor.ShowMessage($"Unknown minor action: {actionType}");
            //             // break;
            //     }
            // }
            // else
            // {
            //     PLog.Warn($"Invalid action type: {actionType}");
            //     isParse = false;
            //     // _mainExecutor.ShowMessage($"Invalid action type: {actionType}");
            // }
            if (!isParse)
            {
                message.ResetMessage();
            }
            return isParse;
        }

        // private async Task ParseFailAction(BattleMessageUnit message)
        // {
        //     string? pokemon = message.NextValue();
        //     string? action = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.FailAction(pokemon!, action!);
        //     }
        // }

        // private async Task ParseBlockAction(BattleMessageUnit message)
        // {
        //     string? pokemon = message.NextValue();
        //     string? effect = message.NextValue();
        //     string? move = message.NextValue();
        //     string? attacker = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.BlockAction(pokemon!, effect!, move, attacker);
        //     }
        // }

        // private async Task ParseNoTarget(BattleMessageUnit message)
        // {
        //     string? pokemon = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.NoTarget(pokemon!);
        //     }
        // }

        // private async Task ParseMiss(BattleMessageUnit message)
        // {
        //     string? source = message.NextValue();
        //     string? target = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.MoveMissed(source!, target!);
        //     }
        // }

        // private async Task ParseDamage(BattleMessageUnit message)
        // {
        //     string? pokemon = message.NextValue();
        //     string? hpStatus = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.DamagePokemon(pokemon!, hpStatus!);
        //     }
        // }

        // private async Task ParseHeal(BattleMessageUnit message)
        // {
        //     string? pokemon = message.NextValue();
        //     string? hpStatus = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.HealPokemon(pokemon!, hpStatus!);
        //     }
        // }

        // private async Task ParseSetHp(BattleMessageUnit message)
        // {
        //     string? pokemon = message.NextValue();
        //     string? hp = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.SetHp(pokemon!, hp!);
        //     }
        // }

        // private async Task ParseStatus(BattleMessageUnit message)
        // {
        //     string? pokemon = message.NextValue();
        //     string? status = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.InflictStatus(pokemon!, status!);
        //     }
        // }

        // private async Task ParseCureStatus(BattleMessageUnit message)
        // {
        //     string? pokemon = message.NextValue();
        //     string? status = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.CureStatus(pokemon!, status!);
        //     }
        // }

        // private async Task ParseCureTeam(BattleMessageUnit message)
        // {
        //     string? pokemon = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.CureTeam(pokemon!);
        //     }
        // }

        // private async Task ParseBoost(BattleMessageUnit message)
        // {
        //     string? pokemon = message.NextValue();
        //     string? stat = message.NextValue();
        //     string? amountStr = message.NextValue();
        //     if (int.TryParse(amountStr, out int amount) && Executor != null)
        //     {
        //         await Executor.BoostStat(pokemon!, stat!, amount);
        //     }
        // }

        // private async Task ParseUnboost(BattleMessageUnit message)
        // {
        //     string? pokemon = message.NextValue();
        //     string? stat = message.NextValue();
        //     string? amountStr = message.NextValue();
        //     if (int.TryParse(amountStr, out int amount) && Executor != null)
        //     {
        //         await Executor.UnboostStat(pokemon!, stat!, amount);
        //     }
        // }

        // private async Task ParseSetBoost(BattleMessageUnit message)
        // {
        //     string? pokemon = message.NextValue();
        //     string? stat = message.NextValue();
        //     string? amountStr = message.NextValue();
        //     if (int.TryParse(amountStr, out int amount) && Executor != null)
        //     {
        //         await Executor.SetBoost(pokemon!, stat!, amount);
        //     }
        // }

        // private async Task ParseSwapBoost(BattleMessageUnit message)
        // {
        //     string? source = message.NextValue();
        //     string? target = message.NextValue();
        //     string? stats = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.SwapBoost(source!, target!, stats!);
        //     }
        // }

        // private async Task ParseInvertBoost(BattleMessageUnit message)
        // {
        //     string? pokemon = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.InvertBoost(pokemon!);
        //     }
        // }

        // private async Task ParseClearBoost(BattleMessageUnit message)
        // {
        //     string? pokemon = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.ClearBoost(pokemon!);
        //     }
        // }

        // private async Task ParseClearAllBoosts(BattleMessageUnit message)
        // {
        //     if (Executor != null)
        //     {
        //         await Executor.ClearAllBoosts();
        //     }
        // }

        // private async Task ParseClearPositiveBoost(BattleMessageUnit message)
        // {
        //     string? target = message.NextValue();
        //     string? pokemon = message.NextValue();
        //     string? effect = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.ClearPositiveBoost(target!, pokemon!, effect!);
        //     }
        // }

        // private async Task ParseClearNegativeBoost(BattleMessageUnit message)
        // {
        //     string? pokemon = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.ClearNegativeBoost(pokemon!);
        //     }
        // }

        // private async Task ParseCopyBoost(BattleMessageUnit message)
        // {
        //     string? source = message.NextValue();
        //     string? target = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.CopyBoost(source!, target!);
        //     }
        // }

        // private async Task ParseWeather(BattleMessageUnit message)
        // {
        //     string? weather = message.NextValue();
        //     bool upkeep = message.ContainsTag("[upkeep]");
        //     if (Executor != null)
        //     {
        //         await Executor.ChangeWeather(weather!, upkeep);
        //     }
        // }

        // private async Task ParseFieldActivate(BattleMessageUnit message)
        // {
        //     string? condition = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.ActiveFieldCondition(condition!);
        //     }
        // }
        // private async Task ParseFieldStart(BattleMessageUnit message)
        // {
        //     string? condition = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.StartFieldCondition(condition!);
        //     }
        // }
        // private async Task ParseFieldEnd(BattleMessageUnit message)
        // {
        //     string? condition = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.EndFieldCondition(condition!);
        //     }
        // }

        // private async Task ParseSideStart(BattleMessageUnit message)
        // {
        //     string? side = message.NextValue();
        //     string? condition = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.StartSideCondition(side!, condition!);
        //     }
        // }

        // private async Task ParseSideEnd(BattleMessageUnit message)
        // {
        //     string? side = message.NextValue();
        //     string? condition = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.EndSideCondition(side!, condition!);
        //     }
        // }

        // private async Task ParseSwapSideConditions(BattleMessageUnit message)
        // {
        //     if (Executor != null)
        //     {
        //         await Executor.SwapSideConditions();
        //     }
        // }

        // private async Task ParseVolatileStart(BattleMessageUnit message)
        // {
        //     string? pokemon = message.NextValue();
        //     string? effect = message.NextValue();
        //     string? ext = message.NextValue();
        //     // |-start|p1a: Falinks|Disable|Throat Chop|[from] ability: Cursed Body|[of] p2a: Gengar
        //     // |-start|p1a: kyogre|Dynamax|
        //     if (Executor != null && !string.IsNullOrEmpty(effect))
        //     {
        //         switch (effect)
        //         {
        //             case "Dynamax":
        //                 // await Executor.ApplyVolatileStatus(pokemon!, effect!, message.From, message.Of, ext);
        //                 await Executor.Transform(pokemon!, effect!);
        //                 break;
        //             default:
        //                 await Executor.ApplyVolatileStatus(pokemon!, effect!, message.From, message.Of, ext);
        //                 break;
        //         }
        //         // await Executor.ApplyVolatileStatus(pokemon!, effect!, message.From, message.Of, ext);
        //     }
        // }

        // private async Task ParseVolatileEnd(BattleMessageUnit message)
        // {
        //     string? pokemon = message.NextValue();
        //     string? effect = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.RemoveVolatileStatus(pokemon!, effect!);
        //     }
        // }

        // private async Task ParseCrit(BattleMessageUnit message)
        // {
        //     string? pokemon = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.CriticalHit(pokemon!);
        //     }
        // }

        // private async Task ParseSuperEffective(BattleMessageUnit message)
        // {
        //     string? pokemon = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.SuperEffective(pokemon!);
        //     }
        // }
        // private async Task ParseResisted(BattleMessageUnit message)
        // {
        //     string? pokemon = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.MoveResisted(pokemon!);
        //     }
        // }

        // private async Task ParseImmune(BattleMessageUnit message)
        // {
        //     string? pokemon = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.MoveImmune(pokemon!);
        //     }
        // }

        // private async Task ParseItem(BattleMessageUnit message)
        // {
        //     string? pokemon = message.NextValue();
        //     string? item = message.NextValue();
        //     string? effect = message.NextValue();
        //     if (Executor != null)
        //     {
        //         if (effect != null && effect.StartsWith("[from]"))
        //         {
        //             effect = effect.Replace("[from]", "").Trim();
        //             await Executor.ItemChanged(pokemon!, item!, effect);
        //         }
        //         else
        //         {
        //             await Executor.ItemRevealed(pokemon!, item!);
        //         }
        //     }
        // }

        // private async Task ParseEndItem(BattleMessageUnit message)
        // {
        //     string? pokemon = message.NextValue();
        //     string? item = message.NextValue();
        //     string? effect = message.NextValue();
        //     if (Executor != null)
        //     {
        //         if (effect != null && effect.StartsWith("[from]"))
        //         {
        //             effect = effect.Replace("[from]", "").Trim();
        //             await Executor.ItemDestroyed(pokemon!, item!, effect);
        //         }
        //         else
        //         {
        //             await Executor.ItemConsumed(pokemon!, item!);
        //         }
        //     }
        // }

        // private async Task ParseAbility(BattleMessageUnit message)
        // {
        //     string? pokemon = message.NextValue();
        //     string? ability = message.NextValue();
        //     string? effect = message.NextValue();
        //     if (Executor != null)
        //     {
        //         if (effect != null && effect.StartsWith("[from]"))
        //         {
        //             effect = effect.Replace("[from]", "").Trim();
        //             await Executor.Ability(pokemon!, ability!, effect);
        //         }
        //         else
        //         {
        //             await Executor.AbilityRevealed(pokemon!, ability!);
        //         }
        //     }
        // }

        // private async Task ParseEndAbility(BattleMessageUnit message)
        // {
        //     string? pokemon = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.AbilitySuppressed(pokemon!);
        //     }
        // }

        // private async Task ParseTransform(BattleMessageUnit message)
        // {
        //     string? pokemon = message.NextValue();
        //     string? species = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.Transform(pokemon!, species!);
        //     }
        // }

        // private async Task ParseMega(BattleMessageUnit message)
        // {
        //     string? pokemon = message.NextValue();
        //     string? megaStone = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.MegaEvolve(pokemon!, megaStone!);
        //     }
        // }

        // private async Task ParsePrimal(BattleMessageUnit message)
        // {
        //     string? pokemon = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.PrimalRevert(pokemon!);
        //     }
        // }

        // private async Task ParseBurst(BattleMessageUnit message)
        // {
        //     string? pokemon = message.NextValue();
        //     string? species = message.NextValue();
        //     string? item = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.UltraBurst(pokemon!, species!, item!);
        //     }
        // }

        // private async Task ParseZPower(BattleMessageUnit message)
        // {
        //     string? pokemon = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.UseZPower(pokemon!);
        //     }
        // }

        // private async Task ParseZBroken(BattleMessageUnit message)
        // {
        //     string? pokemon = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.ZMoveBreakThrough(pokemon!);
        //     }
        // }

        // private async Task ParseActivate(BattleMessageUnit message)
        // {
        //     string? effect = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.ActivateEffect(effect!);
        //     }
        // }

        // private async Task ParseHint(BattleMessageUnit message)
        // {
        //     string? hintMessage = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.ShowHint(hintMessage!);
        //     }
        // }

        // private async Task ParseCenter(BattleMessageUnit message)
        // {
        //     if (Executor != null)
        //     {
        //         await Executor.CenterPokemon();
        //     }
        // }

        // private async Task ParseMessage(BattleMessageUnit message)
        // {
        //     string? customMessage = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.ShowCustomMessage(customMessage!);
        //     }
        // }

        // private async Task ParseCombine(BattleMessageUnit message)
        // {
        //     if (Executor != null)
        //     {
        //         await Executor.CombineMove();
        //     }
        // }

        // private async Task ParseWaiting(BattleMessageUnit message)
        // {
        //     string? source = message.NextValue();
        //     string? target = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.WaitingForTarget(source!, target!);
        //     }
        // }

        // private async Task ParsePrepare(BattleMessageUnit message)
        // {
        //     string? attacker = message.NextValue();
        //     string? move = message.NextValue();
        //     string? defender = message.NextValue();
        //     if (Executor != null)
        //     {
        //         if (defender != null)
        //         {
        //             await Executor.PrepareMove(attacker!, move!, defender);
        //         }
        //         else
        //         {
        //             await Executor.PrepareMove(attacker!, move!);
        //         }
        //     }
        // }

        // private async Task ParseMustRecharge(BattleMessageUnit message)
        // {
        //     string? pokemon = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.MustRecharge(pokemon!);
        //     }
        // }

        // private async Task ParseNothing(BattleMessageUnit message)
        // {
        //     if (Executor != null)
        //     {
        //         await Executor.MoveDidNothing();
        //     }
        // }

        // private async Task ParseHitCount(BattleMessageUnit message)
        // {
        //     string? pokemon = message.NextValue();
        //     string? numHitsStr = message.NextValue();
        //     if (int.TryParse(numHitsStr, out int numHits) && Executor != null)
        //     {
        //         await Executor.MultiHitMove(pokemon!, numHits);
        //     }
        // }

        // private async Task ParseSingleMove(BattleMessageUnit message)
        // {
        //     string? pokemon = message.NextValue();
        //     string? move = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.SingleMoveEffect(pokemon!, move!);
        //     }
        // }

        // private async Task ParseSingleTurn(BattleMessageUnit message)
        // {
        //     string? pokemon = message.NextValue();
        //     string? move = message.NextValue();
        //     if (Executor != null)
        //     {
        //         await Executor.SingleTurnEffect(pokemon!, move!);
        //     }
        // }
    }
}

// runMinor(args: Args, kwArgs: KWArgs, nextArgs?: Args, nextKwargs?: KWArgs) {
// 		if (nextArgs && nextKwargs) {
// 			if (args[2] === "Sturdy" && args[0] === "-activate") {
// 				args[2] = "ability: Sturdy";
// 			}
// 			if (["-crit", "-supereffective", "-resisted"].includes(args[0]) || args[2] === "ability: Sturdy") {
// 				kwArgs.then = ".";
// 			}
// 			if (args[0] === "-damage" && !kwArgs.from && args[1] !== nextArgs[1] && (
// 				["-crit", "-supereffective", "-resisted"].includes(nextArgs[0]) ||
// 				(nextArgs[0] === "-damage" && !nextKwargs.from)
// 			)) {
// 				kwArgs.then = ".";
// 			}
// 			if (args[0] === "-damage" && nextArgs[0] === "-damage" && kwArgs.from && kwArgs.from === nextKwargs.from) {
// 				kwArgs.then = ".";
// 			}
// 			if (args[0] === "-ability" && (args[2] === "Intimidate" || args[3] === "boost")) {
// 				kwArgs.then = ".";
// 			}
// 			if (args[0] === "-unboost" && nextArgs[0] === "-unboost") {
// 				kwArgs.then = ".";
// 			}
// 			if (args[0] === "-boost" && nextArgs[0] === "-boost") {
// 				kwArgs.then = ".";
// 			}
// 			if (args[0] === "-damage" && kwArgs.from === "Leech Seed" && nextArgs[0] === "-heal" && nextKwargs.silent) {
// 				kwArgs.then = ".";
// 			}
// 			if (args[0] === "detailschange" && nextArgs[0] === "-mega") {
// 				if (this.scene.closeMessagebar()) {
// 					this.currentStep--;
// 					return;
// 				}
// 				kwArgs.simult = ".";
// 			}
// 		}
// 		if (kwArgs.then) this.waitForAnimations = false;
// 		if (kwArgs.simult) this.waitForAnimations = "simult";

// 		const CONSUMED = ["eaten", "popped", "consumed", "held up"];
// 		switch (args[0]) {
// 		case "-damage": {
// 			let poke = this.getPokemon(args[1])!;
// 			let damage = poke.healthParse(args[2], true);
// 			if (damage === null) break;
// 			let range = poke.getDamageRange(damage);

// 			if (kwArgs.from) {
// 				let effect = Dex.getEffect(kwArgs.from);
// 				let ofpoke = this.getPokemon(kwArgs.of);
// 				this.activateAbility(ofpoke, effect);
// 				if (effect.effectType === "Item") {
// 					const itemPoke = ofpoke || poke;
// 					if (itemPoke.prevItem !== effect.name && !CONSUMED.includes(itemPoke.prevItemEffect)) {
// 						itemPoke.item = effect.name;
// 					}
// 				}
// 				switch (effect.id) {
// 				case "brn":
// 					this.scene.runStatusAnim("brn" as ID, [poke]);
// 					break;
// 				case "psn":
// 					this.scene.runStatusAnim("psn" as ID, [poke]);
// 					break;
// 				case "baddreams":
// 					this.scene.runStatusAnim("cursed" as ID, [poke]);
// 					break;
// 				case "curse":
// 					this.scene.runStatusAnim("cursed" as ID, [poke]);
// 					break;
// 				case "confusion":
// 					this.scene.runStatusAnim("confusedselfhit" as ID, [poke]);
// 					break;
// 				case "leechseed":
// 					this.scene.runOtherAnim("leech" as ID, [ofpoke!, poke]);
// 					break;
// 				case "bind":
// 				case "wrap":
// 					this.scene.runOtherAnim("bound" as ID, [poke]);
// 					break;
// 				}
// 			} else {
// 				if (this.dex.moves.get(this.lastMove).category !== "Status") {
// 					poke.timesAttacked++;
// 				}
// 				let damageinfo = "" + Pokemon.getFormattedRange(range, damage[1] === 100 ? 0 : 1, "\u2013");
// 				if (damage[1] !== 100) {
// 					let hover = "" + ((damage[0] < 0) ? "\u2212" : "") +
// 						Math.abs(damage[0]) + "/" + damage[1];
// 					if (damage[1] === 48) { // this is a hack
// 						hover += " pixels";
// 					}
// 					// battle-log will convert this into <abbr>
// 					damageinfo = "||" + hover + "||" + damageinfo + "||";
// 				}
// 				args[3] = damageinfo;
// 			}
// 			this.scene.damageAnim(poke, Pokemon.getFormattedRange(range, 0, " to "));
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-heal": {
// 			let poke = this.getPokemon(args[1], Dex.getEffect(kwArgs.from).id === "revivalblessing")!;
// 			let damage = poke.healthParse(args[2], true, true);
// 			if (damage === null) break;
// 			let range = poke.getDamageRange(damage);

// 			if (kwArgs.from) {
// 				let effect = Dex.getEffect(kwArgs.from);
// 				let ofpoke = this.getPokemon(kwArgs.of);
// 				this.activateAbility(ofpoke || poke, effect);
// 				if (effect.effectType === "Item" && !CONSUMED.includes(poke.prevItemEffect)) {
// 					if (poke.prevItem !== effect.name) {
// 						poke.item = effect.name;
// 					}
// 				}
// 				switch (effect.id) {
// 				case "lunardance":
// 					for (let trackedMove of poke.moveTrack) {
// 						trackedMove[1] = 0;
// 					}
// 					// falls through
// 				case "healingwish":
// 					this.lastMove = "healing-wish";
// 					this.scene.runResidualAnim("healingwish" as ID, poke);
// 					poke.side.wisher = null;
// 					poke.statusData.sleepTurns = 0;
// 					poke.statusData.toxicTurns = 0;
// 					break;
// 				case "wish":
// 					this.scene.runResidualAnim("wish" as ID, poke);
// 					break;
// 				case "revivalblessing":
// 					this.scene.runResidualAnim("wish" as ID, poke);
// 					const {siden} = this.parsePokemonId(args[1]);
// 					const side = this.sides[siden];
// 					poke.fainted = false;
// 					poke.status = "";
// 					this.scene.updateSidebar(side);
// 					break;
// 				}
// 			}
// 			this.scene.runOtherAnim("heal" as ID, [poke]);
// 			this.scene.healAnim(poke, Pokemon.getFormattedRange(range, 0, " to "));
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-sethp": {
// 			for (let k = 0; k < 2; k++) {
// 				let cpoke = this.getPokemon(args[1 + 2 * k]);
// 				if (cpoke) {
// 					let damage = cpoke.healthParse(args[2 + 2 * k])!;
// 					let range = cpoke.getDamageRange(damage);
// 					let formattedRange = Pokemon.getFormattedRange(range, 0, " to ");
// 					let diff = damage[0];
// 					if (diff > 0) {
// 						this.scene.healAnim(cpoke, formattedRange);
// 					} else {
// 						this.scene.damageAnim(cpoke, formattedRange);
// 					}
// 				}
// 			}
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-boost": {
// 			let poke = this.getPokemon(args[1])!;
// 			let stat = args[2] as BoostStatName;
// 			if (this.gen === 1 && stat === "spd") break;
// 			if (this.gen === 1 && stat === "spa") stat = "spc";
// 			let amount = parseInt(args[3], 10);
// 			if (amount === 0) {
// 				this.scene.resultAnim(poke, "already " + poke.getBoost(stat), "neutral");
// 				this.log(args, kwArgs);
// 				break;
// 			}
// 			if (!poke.boosts[stat]) {
// 				poke.boosts[stat] = 0;
// 			}
// 			poke.boosts[stat] += amount;

// 			if (!kwArgs.silent && kwArgs.from) {
// 				let effect = Dex.getEffect(kwArgs.from);
// 				let ofpoke = this.getPokemon(kwArgs.of);
// 				if (!(effect.id === "weakarmor" && stat === "spe")) {
// 					this.activateAbility(ofpoke || poke, effect);
// 				}
// 			}
// 			this.scene.resultAnim(poke, poke.getBoost(stat), "good");
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-unboost": {
// 			let poke = this.getPokemon(args[1])!;
// 			let stat = args[2] as BoostStatName;
// 			if (this.gen === 1 && stat === "spd") break;
// 			if (this.gen === 1 && stat === "spa") stat = "spc";
// 			let amount = parseInt(args[3], 10);
// 			if (amount === 0) {
// 				this.scene.resultAnim(poke, "already " + poke.getBoost(stat), "neutral");
// 				this.log(args, kwArgs);
// 				break;
// 			}
// 			if (!poke.boosts[stat]) {
// 				poke.boosts[stat] = 0;
// 			}
// 			poke.boosts[stat] -= amount;

// 			if (!kwArgs.silent && kwArgs.from) {
// 				let effect = Dex.getEffect(kwArgs.from);
// 				let ofpoke = this.getPokemon(kwArgs.of);
// 				this.activateAbility(ofpoke || poke, effect);
// 			}
// 			this.scene.resultAnim(poke, poke.getBoost(stat), "bad");
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-setboost": {
// 			let poke = this.getPokemon(args[1])!;
// 			let stat = args[2] as BoostStatName;
// 			let amount = parseInt(args[3], 10);
// 			poke.boosts[stat] = amount;
// 			this.scene.resultAnim(poke, poke.getBoost(stat), (amount > 0 ? "good" : "bad"));
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-swapboost": {
// 			let poke = this.getPokemon(args[1])!;
// 			let poke2 = this.getPokemon(args[2])!;
// 			let stats = args[3] ? args[3].split(", ") : ["atk", "def", "spa", "spd", "spe", "accuracy", "evasion"];
// 			for (const stat of stats) {
// 				let tmp = poke.boosts[stat];
// 				poke.boosts[stat] = poke2.boosts[stat];
// 				if (!poke.boosts[stat]) delete poke.boosts[stat];
// 				poke2.boosts[stat] = tmp;
// 				if (!poke2.boosts[stat]) delete poke2.boosts[stat];
// 			}
// 			this.scene.resultAnim(poke, "Stats swapped", "neutral");
// 			this.scene.resultAnim(poke2, "Stats swapped", "neutral");

// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-clearpositiveboost": {
// 			let poke = this.getPokemon(args[1])!;
// 			let ofpoke = this.getPokemon(args[2]);
// 			let effect = Dex.getEffect(args[3]);
// 			for (const stat in poke.boosts) {
// 				if (poke.boosts[stat] > 0) delete poke.boosts[stat];
// 			}
// 			this.scene.resultAnim(poke, "Boosts lost", "bad");

// 			if (effect.id) {
// 				switch (effect.id) {
// 				case "spectralthief":
// 					// todo: update StealBoosts so it animates 1st on Spectral Thief
// 					this.scene.runOtherAnim("spectralthiefboost" as ID, [ofpoke!, poke]);
// 					break;
// 				}
// 			}
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-clearnegativeboost": {
// 			let poke = this.getPokemon(args[1])!;
// 			for (const stat in poke.boosts) {
// 				if (poke.boosts[stat] < 0) delete poke.boosts[stat];
// 			}
// 			this.scene.resultAnim(poke, "Restored", "good");

// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-copyboost": {
// 			let poke = this.getPokemon(args[1])!;
// 			let frompoke = this.getPokemon(args[2])!;
// 			if (!kwArgs.silent && kwArgs.from) {
// 				let effect = Dex.getEffect(kwArgs.from);
// 				this.activateAbility(poke, effect);
// 			}
// 			let stats = args[3] ? args[3].split(", ") : ["atk", "def", "spa", "spd", "spe", "accuracy", "evasion"];
// 			for (const stat of stats) {
// 				poke.boosts[stat] = frompoke.boosts[stat];
// 				if (!poke.boosts[stat]) delete poke.boosts[stat];
// 			}
// 			if (this.gen >= 6) {
// 				const volatilesToCopy = ["focusenergy", "gmaxchistrike", "laserfocus"];
// 				for (const volatile of volatilesToCopy) {
// 					if (frompoke.volatiles[volatile]) {
// 						poke.addVolatile(volatile as ID);
// 					} else {
// 						poke.removeVolatile(volatile as ID);
// 					}
// 				}
// 			}
// 			this.scene.resultAnim(poke, "Stats copied", "neutral");

// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-clearboost": {
// 			let poke = this.getPokemon(args[1])!;
// 			poke.boosts = {};
// 			if (!kwArgs.silent && kwArgs.from) {
// 				let effect = Dex.getEffect(kwArgs.from);
// 				let ofpoke = this.getPokemon(kwArgs.of);
// 				this.activateAbility(ofpoke || poke, effect);
// 			}
// 			this.scene.resultAnim(poke, "Stats reset", "neutral");

// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-invertboost": {
// 			let poke = this.getPokemon(args[1])!;
// 			for (const stat in poke.boosts) {
// 				poke.boosts[stat] = -poke.boosts[stat];
// 			}
// 			this.scene.resultAnim(poke, "Stats inverted", "neutral");

// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-clearallboost": {
// 			let timeOffset = this.scene.timeOffset;
// 			for (const active of this.getAllActive()) {
// 				active.boosts = {};
// 				this.scene.timeOffset = timeOffset;
// 				this.scene.resultAnim(active, "Stats reset", "neutral");
// 			}

// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-crit": {
// 			let poke = this.getPokemon(args[1]);
// 			if (poke) this.scene.resultAnim(poke, "Critical hit", "bad");
// 			if (this.activeMoveIsSpread) kwArgs.spread = ".";
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-supereffective": {
// 			let poke = this.getPokemon(args[1]);
// 			if (poke) {
// 				this.scene.resultAnim(poke, "Super-effective", "bad");
// 				if (window.Config?.server?.afd) {
// 					this.scene.runOtherAnim("hitmark" as ID, [poke]);
// 				}
// 			}
// 			if (this.activeMoveIsSpread) kwArgs.spread = ".";
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-resisted": {
// 			let poke = this.getPokemon(args[1]);
// 			if (poke) this.scene.resultAnim(poke, "Resisted", "neutral");
// 			if (this.activeMoveIsSpread) kwArgs.spread = ".";
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-immune": {
// 			let poke = this.getPokemon(args[1])!;
// 			let fromeffect = Dex.getEffect(kwArgs.from);
// 			this.activateAbility(this.getPokemon(kwArgs.of) || poke, fromeffect);
// 			this.log(args, kwArgs);
// 			this.scene.resultAnim(poke, "Immune", "neutral");
// 			break;
// 		}
// 		case "-miss": {
// 			let target = this.getPokemon(args[2]);
// 			if (target) {
// 				this.scene.resultAnim(target, "Missed", "neutral");
// 			}
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-fail": {
// 			let poke = this.getPokemon(args[1])!;
// 			let effect = Dex.getEffect(args[2]);
// 			let fromeffect = Dex.getEffect(kwArgs.from);
// 			let ofpoke = this.getPokemon(kwArgs.of);
// 			if (fromeffect.id === "clearamulet") {
// 				ofpoke!.item = "Clear Amulet";
// 			} else {
// 				this.activateAbility(ofpoke || poke, fromeffect);
// 			}
// 			switch (effect.id) {
// 			case "brn":
// 				this.scene.resultAnim(poke, "Already burned", "neutral");
// 				break;
// 			case "tox":
// 			case "psn":
// 				this.scene.resultAnim(poke, "Already poisoned", "neutral");
// 				break;
// 			case "slp":
// 				if (fromeffect.id === "uproar") {
// 					this.scene.resultAnim(poke, "Failed", "neutral");
// 				} else {
// 					this.scene.resultAnim(poke, "Already asleep", "neutral");
// 				}
// 				break;
// 			case "par":
// 				this.scene.resultAnim(poke, "Already paralyzed", "neutral");
// 				break;
// 			case "frz":
// 				this.scene.resultAnim(poke, "Already frozen", "neutral");
// 				break;
// 			case "unboost":
// 				this.scene.resultAnim(poke, "Stat drop blocked", "neutral");
// 				break;
// 			default:
// 				if (poke) {
// 					this.scene.resultAnim(poke, "Failed", "neutral");
// 				}
// 				break;
// 			}
// 			this.scene.animReset(poke);
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-block": {
// 			let poke = this.getPokemon(args[1])!;
// 			let ofpoke = this.getPokemon(kwArgs.of);
// 			let effect = Dex.getEffect(args[2]);
// 			this.activateAbility(ofpoke || poke, effect);
// 			switch (effect.id) {
// 			case "quickguard":
// 				poke.addTurnstatus("quickguard" as ID);
// 				this.scene.resultAnim(poke, "Quick Guard", "good");
// 				break;
// 			case "wideguard":
// 				poke.addTurnstatus("wideguard" as ID);
// 				this.scene.resultAnim(poke, "Wide Guard", "good");
// 				break;
// 			case "craftyshield":
// 				poke.addTurnstatus("craftyshield" as ID);
// 				this.scene.resultAnim(poke, "Crafty Shield", "good");
// 				break;
// 			case "protect":
// 				poke.addTurnstatus("protect" as ID);
// 				this.scene.resultAnim(poke, "Protected", "good");
// 				break;

// 			case "safetygoggles":
// 				poke.item = "Safety Goggles";
// 				break;
// 			case "protectivepads":
// 				poke.item = "Protective Pads";
// 				break;
// 			case "abilityshield":
// 				poke.item = "Ability Shield";
// 				break;
// 			}
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-center": case "-notarget": case "-ohko":
// 		case "-combine": case "-hitcount": case "-waiting": case "-zbroken": {
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-zpower": {
// 			let poke = this.getPokemon(args[1])!;
// 			this.scene.runOtherAnim("zpower" as ID, [poke]);
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-prepare": {
// 			let poke = this.getPokemon(args[1])!;
// 			let moveid = toID(args[2]);
// 			let target = this.getPokemon(args[3]) || poke.side.foe.active[0] || poke;
// 			this.scene.runPrepareAnim(moveid, poke, target);
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-mustrecharge": {
// 			let poke = this.getPokemon(args[1])!;
// 			poke.addMovestatus("mustrecharge" as ID);
// 			this.scene.updateStatbar(poke);
// 			break;
// 		}
// 		case "-status": {
// 			let poke = this.getPokemon(args[1])!;
// 			let effect = Dex.getEffect(kwArgs.from);
// 			let ofpoke = this.getPokemon(kwArgs.of) || poke;
// 			poke.status = args[2] as StatusName;
// 			this.activateAbility(ofpoke || poke, effect);
// 			if (effect.effectType === "Item") {
// 				ofpoke.item = effect.name;
// 			}

// 			switch (args[2]) {
// 			case "brn":
// 				this.scene.resultAnim(poke, "Burned", "brn");
// 				this.scene.runStatusAnim("brn" as ID, [poke]);
// 				break;
// 			case "tox":
// 				this.scene.resultAnim(poke, "Toxic poison", "psn");
// 				this.scene.runStatusAnim("psn" as ID, [poke]);
// 				poke.statusData.toxicTurns = (effect.name === "Toxic Orb" ? -1 : 0);
// 				break;
// 			case "psn":
// 				this.scene.resultAnim(poke, "Poisoned", "psn");
// 				this.scene.runStatusAnim("psn" as ID, [poke]);
// 				break;
// 			case "slp":
// 				this.scene.resultAnim(poke, "Asleep", "slp");
// 				if (effect.id === "rest") {
// 					poke.statusData.sleepTurns = 0; // for Gen 2 use through Sleep Talk
// 				}
// 				break;
// 			case "par":
// 				this.scene.resultAnim(poke, "Paralyzed", "par");
// 				this.scene.runStatusAnim("par" as ID, [poke]);
// 				break;
// 			case "frz":
// 				this.scene.resultAnim(poke, "Frozen", "frz");
// 				this.scene.runStatusAnim("frz" as ID, [poke]);
// 				break;
// 			default:
// 				this.scene.updateStatbar(poke);
// 				break;
// 			}
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-curestatus": {
// 			let poke = this.getPokemon(args[1])!;
// 			let effect = Dex.getEffect(kwArgs.from);

// 			if (effect.id) {
// 				switch (effect.id) {
// 				case "flamewheel":
// 				case "flareblitz":
// 				case "fusionflare":
// 				case "sacredfire":
// 				case "scald":
// 				case "steameruption":
// 					kwArgs.thaw = ".";
// 					break;
// 				}
// 			}
// 			if (poke) {
// 				poke.status = "";
// 				switch (args[2]) {
// 				case "brn":
// 					this.scene.resultAnim(poke, "Burn cured", "good");
// 					break;
// 				case "tox":
// 				case "psn":
// 					poke.statusData.toxicTurns = 0;
// 					this.scene.resultAnim(poke, "Poison cured", "good");
// 					break;
// 				case "slp":
// 					this.scene.resultAnim(poke, "Woke up", "good");
// 					poke.statusData.sleepTurns = 0;
// 					break;
// 				case "par":
// 					this.scene.resultAnim(poke, "Paralysis cured", "good");
// 					break;
// 				case "frz":
// 					this.scene.resultAnim(poke, "Thawed", "good");
// 					break;
// 				default:
// 					poke.removeVolatile("confusion" as ID);
// 					this.scene.resultAnim(poke, "Cured", "good");
// 				}
// 			}
// 			this.log(args, kwArgs);
// 			break;

// 		}
// 		case "-cureteam": { // For old gens when the whole team was always cured
// 			let poke = this.getPokemon(args[1])!;
// 			for (const target of poke.side.pokemon) {
// 				target.status = "";
// 				this.scene.updateStatbarIfExists(target);
// 			}

// 			this.scene.resultAnim(poke, "Team Cured", "good");
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-item": {
// 			let poke = this.getPokemon(args[1])!;
// 			let item = Dex.items.get(args[2]);
// 			let effect = Dex.getEffect(kwArgs.from);
// 			let ofpoke = this.getPokemon(kwArgs.of);
// 			if (!poke) {
// 				if (effect.id === "frisk") {
// 					const possibleTargets = ofpoke!.side.foe.active.filter(p => p !== null);
// 					if (possibleTargets.length === 1) {
// 						poke = possibleTargets[0]!;
// 					} else {
// 						this.activateAbility(ofpoke!, "Frisk");
// 						this.log(args, kwArgs);
// 						break;
// 					}
// 				} else {
// 					throw new Error("No Pokemon in -item message");
// 				}
// 			}
// 			poke.item = item.name;
// 			poke.itemEffect = "";
// 			poke.removeVolatile("airballoon" as ID);
// 			if (item.id === "airballoon") poke.addVolatile("airballoon" as ID);

// 			if (effect.id) {
// 				switch (effect.id) {
// 				case "pickup":
// 					this.activateAbility(poke, "Pickup");
// 					// falls through
// 				case "recycle":
// 					poke.itemEffect = "found";
// 					this.scene.resultAnim(poke, item.name, "neutral");
// 					break;
// 				case "frisk":
// 					this.activateAbility(ofpoke!, "Frisk");
// 					if (poke && poke !== ofpoke) { // used for gen 6
// 						poke.itemEffect = "frisked";
// 						this.scene.resultAnim(poke, item.name, "neutral");
// 					}
// 					break;
// 				case "magician":
// 				case "pickpocket":
// 					this.activateAbility(poke, effect.name);
// 					// falls through
// 				case "thief":
// 				case "covet":
// 					// simulate the removal of the item from the ofpoke
// 					ofpoke!.item = "";
// 					ofpoke!.itemEffect = "";
// 					ofpoke!.prevItem = item.name;
// 					ofpoke!.prevItemEffect = "stolen";
// 					ofpoke!.addVolatile("itemremoved" as ID);
// 					poke.itemEffect = "stolen";
// 					this.scene.resultAnim(poke, item.name, "neutral");
// 					this.scene.resultAnim(ofpoke!, "Item Stolen", "bad");
// 					break;
// 				case "harvest":
// 					poke.itemEffect = "harvested";
// 					this.activateAbility(poke, "Harvest");
// 					this.scene.resultAnim(poke, item.name, "neutral");
// 					break;
// 				case "bestow":
// 					poke.itemEffect = "bestowed";
// 					this.scene.resultAnim(poke, item.name, "neutral");
// 					break;
// 				case "switcheroo":
// 				case "trick":
// 					poke.itemEffect = "tricked";
// 					// falls through
// 				default:
// 					break;
// 				}
// 			} else {
// 				switch (item.id) {
// 				case "airballoon":
// 					this.scene.resultAnim(poke, "Balloon", "good");
// 					break;
// 				}
// 			}
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-enditem": {
// 			let poke = this.getPokemon(args[1])!;
// 			let item = Dex.items.get(args[2]);
// 			let effect = Dex.getEffect(kwArgs.from);
// 			if (this.gen > 4 || effect.id !== "knockoff") {
// 				poke.item = "";
// 				poke.itemEffect = "";
// 				poke.prevItem = item.name;
// 				poke.prevItemEffect = "";
// 			}
// 			poke.removeVolatile("airballoon" as ID);
// 			poke.addVolatile("itemremoved" as ID);
// 			if (kwArgs.eat) {
// 				poke.prevItemEffect = "eaten";
// 				this.scene.runOtherAnim("consume" as ID, [poke]);
// 				this.lastMove = item.id;
// 			} else if (kwArgs.weaken) {
// 				poke.prevItemEffect = "eaten";
// 				this.lastMove = item.id;
// 			} else if (effect.id) {
// 				switch (effect.id) {
// 				case "fling":
// 					poke.prevItemEffect = "flung";
// 					break;
// 				case "knockoff":
// 					if (this.gen <= 4) {
// 						poke.itemEffect = "knocked off";
// 					} else {
// 						poke.prevItemEffect = "knocked off";
// 					}
// 					this.scene.runOtherAnim("itemoff" as ID, [poke]);
// 					this.scene.resultAnim(poke, "Item knocked off", "neutral");
// 					break;
// 				case "stealeat":
// 					poke.prevItemEffect = "stolen";
// 					break;
// 				case "gem":
// 					poke.prevItemEffect = "consumed";
// 					break;
// 				case "incinerate":
// 					poke.prevItemEffect = "incinerated";
// 					break;
// 				}
// 			} else {
// 				switch (item.id) {
// 				case "airballoon":
// 					poke.prevItemEffect = "popped";
// 					poke.removeVolatile("airballoon" as ID);
// 					this.scene.resultAnim(poke, "Balloon popped", "neutral");
// 					break;
// 				case "focussash":
// 					poke.prevItemEffect = "consumed";
// 					this.scene.resultAnim(poke, "Sash", "neutral");
// 					break;
// 				case "focusband":
// 					this.scene.resultAnim(poke, "Focus Band", "neutral");
// 					break;
// 				case "redcard":
// 					poke.prevItemEffect = "held up";
// 					break;
// 				default:
// 					poke.prevItemEffect = "consumed";
// 					break;
// 				}
// 			}
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-ability": {
// 			let poke = this.getPokemon(args[1])!;
// 			let ability = Dex.abilities.get(args[2]);
// 			let effect = Dex.getEffect(kwArgs.from);
// 			let ofpoke = this.getPokemon(kwArgs.of);
// 			poke.rememberAbility(ability.name, effect.id && !kwArgs.fail);

// 			if (kwArgs.silent) {
// 				// do nothing
// 			} else if (effect.id) {
// 				switch (effect.id) {
// 				case "trace":
// 					this.activateAbility(poke, "Trace");
// 					this.scene.wait(500);
// 					this.activateAbility(poke, ability.name, true);
// 					ofpoke!.rememberAbility(ability.name);
// 					break;
// 				case "powerofalchemy":
// 				case "receiver":
// 					this.activateAbility(poke, effect.name);
// 					this.scene.wait(500);
// 					this.activateAbility(poke, ability.name, true);
// 					ofpoke!.rememberAbility(ability.name);
// 					break;
// 				case "roleplay":
// 					this.activateAbility(poke, ability.name, true);
// 					ofpoke!.rememberAbility(ability.name);
// 					break;
// 				case "desolateland":
// 				case "primordialsea":
// 				case "deltastream":
// 					if (kwArgs.fail) {
// 						this.activateAbility(poke, ability.name);
// 					}
// 					break;
// 				default:
// 					this.activateAbility(poke, ability.name);
// 					break;
// 				}
// 			} else {
// 				this.activateAbility(poke, ability.name);
// 			}
// 			this.scene.updateWeather();
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-endability": {
// 			// deprecated; use |-start| for Gastro Acid
// 			// and the third arg of |-ability| for Entrainment et al
// 			let poke = this.getPokemon(args[1])!;
// 			let ability = Dex.abilities.get(args[2]);
// 			poke.ability = "(suppressed)";

// 			if (ability.id) {
// 				if (!poke.baseAbility) poke.baseAbility = ability.name;
// 			}
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "detailschange": {
// 			let poke = this.getPokemon(args[1])!;
// 			poke.removeVolatile("formechange" as ID);
// 			poke.removeVolatile("typeadd" as ID);
// 			poke.removeVolatile("typechange" as ID);

// 			let newSpeciesForme = args[2];
// 			let commaIndex = newSpeciesForme.indexOf(",");
// 			if (commaIndex !== -1) {
// 				let level = newSpeciesForme.substr(commaIndex + 1).trim();
// 				if (level.charAt(0) === "L") {
// 					poke.level = parseInt(level.substr(1), 10);
// 				}
// 				newSpeciesForme = args[2].substr(0, commaIndex);
// 			}
// 			let species = this.dex.species.get(newSpeciesForme);
// 			if (nextArgs) {
// 				if (nextArgs[0] === "-mega") {
// 					species = this.dex.species.get(this.dex.items.get(nextArgs[3]).megaStone);
// 				} else if (nextArgs[0] === "-primal" && nextArgs.length > 2) {
// 					if (nextArgs[2] === "Red Orb") species = this.dex.species.get("Groudon-Primal");
// 					if (nextArgs[2] === "Blue Orb") species = this.dex.species.get("Kyogre-Primal");
// 				}
// 			}

// 			poke.speciesForme = newSpeciesForme;
// 			poke.ability = poke.baseAbility = (species.abilities ? species.abilities["0"] : "");

// 			poke.details = args[2];
// 			poke.searchid = args[1].substr(0, 2) + args[1].substr(3) + "|" + args[2];

// 			this.scene.animTransform(poke, true, true);
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-transform": {
// 			let poke = this.getPokemon(args[1])!;
// 			let tpoke = this.getPokemon(args[2])!;
// 			let effect = Dex.getEffect(kwArgs.from);
// 			if (poke === tpoke) throw new Error("Transforming into self");

// 			if (!kwArgs.silent) {
// 				this.activateAbility(poke, effect);
// 			}

// 			poke.boosts = {...tpoke.boosts};
// 			poke.copyTypesFrom(tpoke, true);
// 			poke.ability = tpoke.ability;
// 			poke.timesAttacked = tpoke.timesAttacked;
// 			const targetForme = tpoke.volatiles.formechange;
// 			const speciesForme = (targetForme && !targetForme[1].endsWith("-Gmax")) ? targetForme[1] : tpoke.speciesForme;
// 			const pokemon = tpoke;
// 			const shiny = tpoke.shiny;
// 			const gender = tpoke.gender;
// 			const level = tpoke.level;
// 			poke.addVolatile("transform" as ID, pokemon, shiny, gender, level);
// 			poke.addVolatile("formechange" as ID, speciesForme);
// 			for (const trackedMove of tpoke.moveTrack) {
// 				poke.rememberMove(trackedMove[0], 0);
// 			}
// 			this.scene.animTransform(poke);
// 			this.scene.resultAnim(poke, "Transformed", "good");
// 			this.log(["-transform", args[1], args[2], tpoke.speciesForme], kwArgs);
// 			break;
// 		}
// 		case "-formechange": {
// 			let poke = this.getPokemon(args[1])!;
// 			let species = Dex.species.get(args[2]);
// 			let fromeffect = Dex.getEffect(kwArgs.from);
// 			if (!poke.getSpeciesForme().endsWith("-Gmax") && !species.name.endsWith("-Gmax")) {
// 				poke.removeVolatile("typeadd" as ID);
// 				poke.removeVolatile("typechange" as ID);
// 				if (this.gen >= 6) poke.removeVolatile("autotomize" as ID);
// 			}

// 			if (!kwArgs.silent) {
// 				this.activateAbility(poke, fromeffect);
// 			}
// 			poke.addVolatile("formechange" as ID, species.name); // the formechange volatile reminds us to revert the sprite change on switch-out
// 			this.scene.animTransform(poke, true);
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-mega": {
// 			let poke = this.getPokemon(args[1])!;
// 			let item = Dex.items.get(args[3]);
// 			if (args[3]) {
// 				poke.item = item.name;
// 			}
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-primal": case "-burst": {
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-terastallize": {
// 			let poke = this.getPokemon(args[1])!;
// 			let type = Dex.types.get(args[2]).name;
// 			let lockForme = false;
// 			poke.removeVolatile("typeadd" as ID);
// 			poke.teraType = type;
// 			poke.terastallized = type;
// 			poke.details += `, tera:${type}`;
// 			poke.searchid += `, tera:${type}`;
// 			if (poke.speciesForme.startsWith("Morpeko")) {
// 				lockForme = true;
// 				poke.speciesForme = poke.getSpeciesForme();
// 				poke.details = poke.details.replace("Morpeko", poke.speciesForme);
// 				poke.searchid = `${poke.ident}|${poke.details}`;
// 				delete poke.volatiles["formechange"];
// 			}
// 			this.scene.animTransform(poke, true, lockForme);
// 			this.scene.resetStatbar(poke);
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-start": {
// 			let poke = this.getPokemon(args[1])!;
// 			let effect = Dex.getEffect(args[2]);
// 			let ofpoke = this.getPokemon(kwArgs.of);
// 			let fromeffect = Dex.getEffect(kwArgs.from);

// 			this.activateAbility(poke, effect);
// 			this.activateAbility(ofpoke || poke, fromeffect);
// 			switch (effect.id) {
// 			case "typechange":
// 				if (poke.terastallized) break;
// 				if (ofpoke && fromeffect.id === "reflecttype") {
// 					poke.copyTypesFrom(ofpoke);
// 				} else {
// 					const types = Dex.sanitizeName(args[3] || "???");
// 					poke.removeVolatile("typeadd" as ID);
// 					poke.addVolatile("typechange" as ID, types);
// 					if (!kwArgs.silent) {
// 						this.scene.typeAnim(poke, types);
// 					}
// 				}
// 				this.scene.updateStatbar(poke);
// 				break;
// 			case "typeadd":
// 				const type = Dex.sanitizeName(args[3]);
// 				poke.addVolatile("typeadd" as ID, type);
// 				if (kwArgs.silent) break;
// 				this.scene.typeAnim(poke, type);
// 				break;
// 			case "dynamax":
// 				poke.addVolatile("dynamax" as ID, !!args[3]);
// 				this.scene.animTransform(poke, true);
// 				break;
// 			case "powertrick":
// 				this.scene.resultAnim(poke, "Power Trick", "neutral");
// 				break;
// 			case "foresight":
// 			case "miracleeye":
// 				this.scene.resultAnim(poke, "Identified", "bad");
// 				break;
// 			case "telekinesis":
// 				this.scene.resultAnim(poke, "Telekinesis", "neutral");
// 				break;
// 			case "confusion":
// 				if (!kwArgs.already) {
// 					this.scene.runStatusAnim("confused" as ID, [poke]);
// 					this.scene.resultAnim(poke, "Confused", "bad");
// 				}
// 				break;
// 			case "leechseed":
// 				this.scene.updateStatbar(poke);
// 				break;
// 			case "healblock":
// 				this.scene.resultAnim(poke, "Heal Block", "bad");
// 				break;
// 			case "yawn":
// 				this.scene.resultAnim(poke, "Drowsy", "slp");
// 				break;
// 			case "taunt":
// 				this.scene.resultAnim(poke, "Taunted", "bad");
// 				break;
// 			case "imprison":
// 				this.scene.resultAnim(poke, "Imprisoning", "good");
// 				break;
// 			case "disable":
// 				this.scene.resultAnim(poke, "Disabled", "bad");
// 				break;
// 			case "embargo":
// 				this.scene.resultAnim(poke, "Embargo", "bad");
// 				break;
// 			case "torment":
// 				this.scene.resultAnim(poke, "Tormented", "bad");
// 				break;
// 			case "ingrain":
// 				this.scene.resultAnim(poke, "Ingrained", "good");
// 				break;
// 			case "aquaring":
// 				this.scene.resultAnim(poke, "Aqua Ring", "good");
// 				break;
// 			case "stockpile1":
// 				this.scene.resultAnim(poke, "Stockpile", "good");
// 				break;
// 			case "stockpile2":
// 				poke.removeVolatile("stockpile1" as ID);
// 				this.scene.resultAnim(poke, "Stockpile&times;2", "good");
// 				break;
// 			case "stockpile3":
// 				poke.removeVolatile("stockpile2" as ID);
// 				this.scene.resultAnim(poke, "Stockpile&times;3", "good");
// 				break;
// 			case "perish0":
// 				poke.removeVolatile("perish1" as ID);
// 				break;
// 			case "perish1":
// 				poke.removeVolatile("perish2" as ID);
// 				this.scene.resultAnim(poke, "Perish next turn", "bad");
// 				break;
// 			case "perish2":
// 				poke.removeVolatile("perish3" as ID);
// 				this.scene.resultAnim(poke, "Perish in 2", "bad");
// 				break;
// 			case "perish3":
// 				if (!kwArgs.silent) this.scene.resultAnim(poke, "Perish in 3", "bad");
// 				break;
// 			case "encore":
// 				this.scene.resultAnim(poke, "Encored", "bad");
// 				break;
// 			case "bide":
// 				this.scene.resultAnim(poke, "Bide", "good");
// 				break;
// 			case "attract":
// 				this.scene.resultAnim(poke, "Attracted", "bad");
// 				break;
// 			case "autotomize":
// 				this.scene.resultAnim(poke, "Lightened", "good");
// 				if (poke.volatiles.autotomize) {
// 					poke.volatiles.autotomize[1]++;
// 				} else {
// 					poke.addVolatile("autotomize" as ID, 1);
// 				}
// 				break;
// 			case "focusenergy":
// 				this.scene.resultAnim(poke, "+Crit rate", "good");
// 				break;
// 			case "curse":
// 				this.scene.resultAnim(poke, "Cursed", "bad");
// 				break;
// 			case "nightmare":
// 				this.scene.resultAnim(poke, "Nightmare", "bad");
// 				break;
// 			case "magnetrise":
// 				this.scene.resultAnim(poke, "Magnet Rise", "good");
// 				break;
// 			case "smackdown":
// 				this.scene.resultAnim(poke, "Smacked Down", "bad");
// 				poke.removeVolatile("magnetrise" as ID);
// 				poke.removeVolatile("telekinesis" as ID);
// 				if (poke.lastMove === "fly" || poke.lastMove === "bounce") this.scene.animReset(poke);
// 				break;
// 			case "substitute":
// 				if (kwArgs.damage) {
// 					this.scene.resultAnim(poke, "Damage", "bad");
// 				} else if (kwArgs.block) {
// 					this.scene.resultAnim(poke, "Blocked", "neutral");
// 				}
// 				break;

// 			// Gen 1-2
// 			case "mist":
// 				this.scene.resultAnim(poke, "Mist", "good");
// 				break;
// 			// Gen 1
// 			case "lightscreen":
// 				this.scene.resultAnim(poke, "Light Screen", "good");
// 				break;
// 			case "reflect":
// 				this.scene.resultAnim(poke, "Reflect", "good");
// 				break;
// 			}
// 			if (!(effect.id === "typechange" && poke.terastallized)) {
// 				poke.addVolatile(effect.id);
// 			}
// 			this.scene.updateStatbar(poke);
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-end": {
// 			let poke = this.getPokemon(args[1])!;
// 			let effect = Dex.getEffect(args[2]);
// 			let fromeffect = Dex.getEffect(kwArgs.from);
// 			poke.removeVolatile(effect.id);

// 			if (kwArgs.silent && !(effect.id === "protosynthesis" || effect.id === "quarkdrive")) {
// 				// do nothing
// 			} else {
// 				switch (effect.id) {
// 				case "dynamax":
// 					this.scene.animTransform(poke);
// 					break;
// 				case "powertrick":
// 					this.scene.resultAnim(poke, "Power Trick", "neutral");
// 					break;
// 				case "telekinesis":
// 					this.scene.resultAnim(poke, "Telekinesis&nbsp;ended", "neutral");
// 					break;
// 				case "skydrop":
// 					if (kwArgs.interrupt) {
// 						this.scene.anim(poke, {time: 100});
// 					}
// 					break;
// 				case "confusion":
// 					this.scene.resultAnim(poke, "Confusion&nbsp;ended", "good");
// 					break;
// 				case "leechseed":
// 					if (fromeffect.id === "rapidspin") {
// 						this.scene.resultAnim(poke, "De-seeded", "good");
// 					}
// 					break;
// 				case "healblock":
// 					this.scene.resultAnim(poke, "Heal Block ended", "good");
// 					break;
// 				case "attract":
// 					this.scene.resultAnim(poke, "Attract&nbsp;ended", "good");
// 					break;
// 				case "taunt":
// 					this.scene.resultAnim(poke, "Taunt&nbsp;ended", "good");
// 					break;
// 				case "disable":
// 					this.scene.resultAnim(poke, "Disable&nbsp;ended", "good");
// 					break;
// 				case "embargo":
// 					this.scene.resultAnim(poke, "Embargo ended", "good");
// 					break;
// 				case "torment":
// 					this.scene.resultAnim(poke, "Torment&nbsp;ended", "good");
// 					break;
// 				case "encore":
// 					this.scene.resultAnim(poke, "Encore&nbsp;ended", "good");
// 					break;
// 				case "bide":
// 					this.scene.runOtherAnim("bideunleash" as ID, [poke]);
// 					break;
// 				case "illusion":
// 					this.scene.resultAnim(poke, "Illusion ended", "bad");
// 					poke.rememberAbility("Illusion");
// 					break;
// 				case "slowstart":
// 					this.scene.resultAnim(poke, "Slow Start ended", "good");
// 					break;
// 				case "perishsong": // for backwards compatibility
// 					poke.removeVolatile("perish3" as ID);
// 					break;
// 				case "substitute":
// 					this.scene.resultAnim(poke, "Faded", "bad");
// 					break;
// 				case "stockpile":
// 					poke.removeVolatile("stockpile1" as ID);
// 					poke.removeVolatile("stockpile2" as ID);
// 					poke.removeVolatile("stockpile3" as ID);
// 					break;
// 				case "protosynthesis":
// 					poke.removeVolatile("protosynthesisatk" as ID);
// 					poke.removeVolatile("protosynthesisdef" as ID);
// 					poke.removeVolatile("protosynthesisspa" as ID);
// 					poke.removeVolatile("protosynthesisspd" as ID);
// 					poke.removeVolatile("protosynthesisspe" as ID);
// 					break;
// 				case "quarkdrive":
// 					poke.removeVolatile("quarkdriveatk" as ID);
// 					poke.removeVolatile("quarkdrivedef" as ID);
// 					poke.removeVolatile("quarkdrivespa" as ID);
// 					poke.removeVolatile("quarkdrivespd" as ID);
// 					poke.removeVolatile("quarkdrivespe" as ID);
// 					break;
// 				default:
// 					if (effect.effectType === "Move") {
// 						if (effect.name === "Doom Desire") {
// 							this.scene.runOtherAnim("doomdesirehit" as ID, [poke]);
// 						}
// 						if (effect.name === "Future Sight") {
// 							this.scene.runOtherAnim("futuresighthit" as ID, [poke]);
// 						}
// 					}
// 				}
// 			}
// 			this.scene.updateStatbar(poke);
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-singleturn": {
// 			let poke = this.getPokemon(args[1])!;
// 			let effect = Dex.getEffect(args[2]);
// 			if (effect.id === "roost" && !poke.getTypeList().includes("Flying")) {
// 				break;
// 			}
// 			poke.addTurnstatus(effect.id);
// 			switch (effect.id) {
// 			case "roost":
// 				this.scene.resultAnim(poke, "Landed", "neutral");
// 				break;
// 			case "quickguard":
// 				this.scene.resultAnim(poke, "Quick Guard", "good");
// 				break;
// 			case "wideguard":
// 				this.scene.resultAnim(poke, "Wide Guard", "good");
// 				break;
// 			case "craftyshield":
// 				this.scene.resultAnim(poke, "Crafty Shield", "good");
// 				break;
// 			case "matblock":
// 				this.scene.resultAnim(poke, "Mat Block", "good");
// 				break;
// 			case "protect":
// 				this.scene.resultAnim(poke, "Protected", "good");
// 				break;
// 			case "endure":
// 				this.scene.resultAnim(poke, "Enduring", "good");
// 				break;
// 			case "helpinghand":
// 				this.scene.resultAnim(poke, "Helping Hand", "good");
// 				break;
// 			case "focuspunch":
// 				this.scene.resultAnim(poke, "Focusing", "neutral");
// 				poke.rememberMove(effect.name, 0);
// 				break;
// 			case "shelltrap":
// 				this.scene.resultAnim(poke, "Trap set", "neutral");
// 				poke.rememberMove(effect.name, 0);
// 				break;
// 			case "beakblast":
// 				this.scene.runOtherAnim("bidecharge" as ID, [poke]);
// 				this.scene.resultAnim(poke, "Beak Blast", "neutral");
// 				break;
// 			}
// 			this.scene.updateStatbar(poke);
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-singlemove": {
// 			let poke = this.getPokemon(args[1])!;
// 			let effect = Dex.getEffect(args[2]);
// 			poke.addMovestatus(effect.id);
// 			switch (effect.id) {
// 			case "grudge":
// 				this.scene.resultAnim(poke, "Grudge", "neutral");
// 				break;
// 			case "destinybond":
// 				this.scene.resultAnim(poke, "Destiny Bond", "neutral");
// 				break;
// 			}
// 			this.scene.updateStatbar(poke);
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-activate": {
// 			let poke = this.getPokemon(args[1])!;
// 			let effect = Dex.getEffect(args[2]);
// 			let target = this.getPokemon(args[3]);
// 			this.activateAbility(poke, effect);
// 			switch (effect.id) {
// 			case "poltergeist":
// 				poke.item = kwArgs.item;
// 				poke.itemEffect = "disturbed";
// 				break;
// 			case "grudge":
// 				poke.rememberMove(kwArgs.move, Infinity);
// 				break;
// 			case "substitute":
// 				if (kwArgs.damage) {
// 					this.scene.resultAnim(poke, "Damage", "bad");
// 				} else if (kwArgs.block) {
// 					this.scene.resultAnim(poke, "Blocked", "neutral");
// 				}
// 				break;
// 			case "attract":
// 				this.scene.runStatusAnim("attracted" as ID, [poke]);
// 				break;
// 			case "bide":
// 				this.scene.runOtherAnim("bidecharge" as ID, [poke]);
// 				break;

// 			// move activations
// 			case "aromatherapy":
// 				this.scene.resultAnim(poke, "Team Cured", "good");
// 				break;
// 			case "healbell":
// 				this.scene.resultAnim(poke, "Team Cured", "good");
// 				break;
// 			case "brickbreak":
// 				target!.side.removeSideCondition("Reflect");
// 				target!.side.removeSideCondition("LightScreen");
// 				break;
// 			case "hyperspacefury":
// 			case "hyperspacehole":
// 			case "phantomforce":
// 			case "shadowforce":
// 			case "feint":
// 				this.scene.resultAnim(poke, "Protection broken", "bad");
// 				poke.removeTurnstatus("protect" as ID);
// 				for (const curTarget of poke.side.pokemon) {
// 					curTarget.removeTurnstatus("wideguard" as ID);
// 					curTarget.removeTurnstatus("quickguard" as ID);
// 					curTarget.removeTurnstatus("craftyshield" as ID);
// 					curTarget.removeTurnstatus("matblock" as ID);
// 					this.scene.updateStatbar(curTarget);
// 				}
// 				break;
// 			case "eeriespell":
// 			case "gmaxdepletion":
// 			case "spite":
// 				let move = Dex.moves.get(kwArgs.move).name;
// 				let pp = Number(kwArgs.number);
// 				if (isNaN(pp)) pp = 4;
// 				poke.rememberMove(move, pp);
// 				break;
// 			case "gravity":
// 				poke.removeVolatile("magnetrise" as ID);
// 				poke.removeVolatile("telekinesis" as ID);
// 				this.scene.anim(poke, {time: 100});
// 				break;
// 			case "skillswap": case "wanderingspirit":
// 				if (this.gen <= 4) break;
// 				let pokeability = Dex.sanitizeName(kwArgs.ability) || target!.ability;
// 				let targetability = Dex.sanitizeName(kwArgs.ability2) || poke.ability;
// 				if (pokeability) {
// 					poke.ability = pokeability;
// 					if (!target!.baseAbility) target!.baseAbility = pokeability;
// 				}
// 				if (targetability) {
// 					target!.ability = targetability;
// 					if (!poke.baseAbility) poke.baseAbility = targetability;
// 				}
// 				if (poke.side !== target!.side) {
// 					this.activateAbility(poke, pokeability, true);
// 					this.activateAbility(target, targetability, true);
// 				}
// 				break;

// 			// ability activations
// 			case "electromorphosis":
// 			case "windpower":
// 				poke.addMovestatus("charge" as ID);
// 				break;
// 			case "forewarn":
// 				if (target) {
// 					target.rememberMove(kwArgs.move, 0);
// 				} else {
// 					let foeActive = [] as Pokemon[];
// 					for (const maybeTarget of poke.side.foe.active) {
// 						if (maybeTarget && !maybeTarget.fainted) foeActive.push(maybeTarget);
// 					}
// 					if (foeActive.length === 1) {
// 						foeActive[0].rememberMove(kwArgs.move, 0);
// 					}
// 				}
// 				break;
// 			case "lingeringaroma":
// 			case "mummy":
// 				if (!kwArgs.ability) break; // if Mummy activated but failed, no ability will have been sent
// 				let ability = Dex.abilities.get(kwArgs.ability);
// 				this.activateAbility(target, ability.name);
// 				this.activateAbility(poke, effect.name);
// 				this.scene.wait(700);
// 				this.activateAbility(target, effect.name, true);
// 				break;

// 			// item activations
// 			case "leppaberry":
// 			case "mysteryberry":
// 				poke.rememberMove(kwArgs.move, effect.id === "leppaberry" ? -10 : -5);
// 				break;
// 			case "focusband":
// 				poke.item = "Focus Band";
// 				break;
// 			case "quickclaw":
// 				poke.item = "Quick Claw";
// 				break;
// 			case "abilityshield":
// 				poke.item = "Ability Shield";
// 				break;
// 			default:
// 				if (kwArgs.broken) { // for custom moves that break protection
// 					this.scene.resultAnim(poke, "Protection broken", "bad");
// 				}
// 			}
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-sidestart": {
// 			let side = this.getSide(args[1]);
// 			let effect = Dex.getEffect(args[2]);
// 			side.addSideCondition(effect, !!kwArgs.persistent);

// 			switch (effect.id) {
// 			case "tailwind":
// 			case "auroraveil":
// 			case "reflect":
// 			case "lightscreen":
// 			case "safeguard":
// 			case "mist":
// 			case "gmaxwildfire":
// 			case "gmaxvolcalith":
// 			case "gmaxvinelash":
// 			case "gmaxcannonade":
// 			case "grasspledge":
// 			case "firepledge":
// 			case "waterpledge":
// 				this.scene.updateWeather();
// 				break;
// 			}
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-sideend": {
// 			let side = this.getSide(args[1]);
// 			let effect = Dex.getEffect(args[2]);
// 			// let from = Dex.getEffect(kwArgs.from);
// 			// let ofpoke = this.getPokemon(kwArgs.of);
// 			side.removeSideCondition(effect.name);
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-swapsideconditions": {
// 			this.swapSideConditions();
// 			this.scene.updateWeather();
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-weather": {
// 			let effect = Dex.getEffect(args[1]);
// 			let poke = this.getPokemon(kwArgs.of) || undefined;
// 			let ability = Dex.getEffect(kwArgs.from);
// 			if (!effect.id || effect.id === "none") {
// 				kwArgs.from = this.weather;
// 			}
// 			this.changeWeather(effect.name, poke, !!kwArgs.upkeep, ability);
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-fieldstart": {
// 			let effect = Dex.getEffect(args[1]);
// 			let poke = this.getPokemon(kwArgs.of);
// 			let fromeffect = Dex.getEffect(kwArgs.from);
// 			this.activateAbility(poke, fromeffect);
// 			let minTimeLeft = 5;
// 			let maxTimeLeft = 0;
// 			if (effect.id.endsWith("terrain")) {
// 				for (let i = this.pseudoWeather.length - 1; i >= 0; i--) {
// 					let pwID = toID(this.pseudoWeather[i][0]);
// 					if (pwID.endsWith("terrain")) {
// 						this.pseudoWeather.splice(i, 1);
// 						continue;
// 					}
// 				}
// 				if (this.gen > 6) maxTimeLeft = 8;
// 			}
// 			if (kwArgs.persistent) minTimeLeft += 2;
// 			this.addPseudoWeather(effect.name, minTimeLeft, maxTimeLeft);

// 			switch (effect.id) {
// 			case "gravity":
// 				if (this.seeking !== null) break;
// 				for (const active of this.getAllActive()) {
// 					this.scene.runOtherAnim("gravity" as ID, [active]);
// 				}
// 				break;
// 			}
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-fieldend": {
// 			let effect = Dex.getEffect(args[1]);
// 			// let poke = this.getPokemon(kwArgs.of);
// 			this.removePseudoWeather(effect.name);
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-fieldactivate": {
// 			let effect = Dex.getEffect(args[1]);
// 			switch (effect.id) {
// 			case "perishsong":
// 				this.scene.updateStatbars();
// 				break;
// 			}
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		case "-anim": {
// 			let poke = this.getPokemon(args[1])!;
// 			let move = Dex.moves.get(args[2]);
// 			if (this.checkActive(poke)) return;
// 			let poke2 = this.getPokemon(args[3]);
// 			this.scene.beforeMove(poke);
// 			this.animateMove(poke, move, poke2, kwArgs);
// 			this.scene.afterMove(poke);
// 			break;
// 		}
// 		case "-hint": case "-message": case "-candynamax": {
// 			this.log(args, kwArgs);
// 			break;
// 		}
// 		default: {
// 			throw new Error(`Unrecognized minor action: ${args[0]}`);
// 			break;
// 		}}
// 	}