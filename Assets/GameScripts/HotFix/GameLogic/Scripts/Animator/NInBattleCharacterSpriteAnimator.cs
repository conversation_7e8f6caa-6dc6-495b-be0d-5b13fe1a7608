using UnityEngine;
using Role;
using DG.Tweening;
using System;
using Cysharp.Threading.Tasks;

// 角色方向枚举
// public enum NinCharacterAnimatorDirection
// {
//     Down,
//     Left,
//     Right,
//     Up
// }

public class NinBattleCharacterSpriteAnimator : NinSpriteAnimator
{
    // private Dictionary<NinCharacterAnimatorDirection, Sprite[]> characterSprites = new Dictionary<NinCharacterAnimatorDirection, Sprite[]>(); // 存储不同方向的动画帧
    // 将数组初始化为空数组（使用 new 关键字）
    // private Sprite[] battleCharacterFrontSprites = new Sprite[0];
    // private Sprite[] battleCharacterBackSprites = new Sprite[0];
    private Vector3 _originRendererLocalPosition;
    private Vector3 _originRendererLocalScale;
    // private NinSpriteAnimator _spriteAnimator;
    // private Texture2DSheetAnimator _texture2DSheetAnimator;
    protected GameObject rendererGameObject;
    // private Sprite[] currentCharacterSprites = new Sprite[0];
    // public string characterName; // 角色名称（用于加载特定的角色图片）
    // public bool battle; //是否是战斗图
    // private IAnimationSpriteResourceInfo _spriteResourceInfo;
    // public IAnimationSpriteResourceInfo SpriteResourceInfo
    // {
    //     get
    //     {
    //         return _spriteResourceInfo;
    //     }
    //     // set {
    //     //     _spriteResourceInfo = value;
    //     //     LoadCharacterSprites();
    //     // }
    // }
    private bool _flipX = false;
    public bool flipX
    {
        get
        {
            return _flipX;
        }
        set
        {
            _flipX = value;
            SetFlipX(flipX);
            // = _flipX ? Vector3.zero : new Vector3(180, 0, 0)
            // rendererGameObject.transform.rotation.x = _flipX ? 180 : 0 ;
        }
    }
    public void SetTeraEffect(Texture2D? texture) {
        if(texture == null) {
            _spriteAnimator.SetOverlayStrength(0);
        } else {
            _spriteAnimator.SetOverlayTexture(texture);
            _spriteAnimator.SetOverlayStrength(0.5f);
        }
    }
    //图片是否加载完成
    // public async UniTask SetSpriteResourceInfo(IAnimationSpriteResourceInfo info)
    // {
    //     _spriteResourceInfo = info;
    //     await LoadCharacterSprites();
    // }
    // public void Awake() {
    //     _texture2DSheetAnimator = new Texture2DSheetAnimator();
    // }
    // public void 
    // private SpriteRenderer spriteRenderer;
    // private float animationSpeed = 0.1f;  // 每帧的持续时间
    // private float defaultAnimationSpeed = 0.1f;
    // private float timer;
    // private int frameIndex = 0;

    // private NinCharacterAnimatorDirection currentAnimatorDirection;  // 当前动画的方向
    // private bool isPaused = false; // 是否暂停
    // private bool isLooping = true;  // 跟踪是否循环播放动画
    // private int frameCount = 4;

    // private float moveSpeed = 2.0f;
    // private Vector2 direction;

    void Start()
    {
        rendererGameObject = _meshRenderer.gameObject;
        _originRendererLocalPosition = _meshRenderer.gameObject.transform.localPosition;
        SetFlipX(flipX);
        RefreshOriginScale();
    }
    public void RefreshOriginScale()
    {
        _originRendererLocalScale = rendererGameObject.transform.localScale;
    }
    public void RefreshLocalPosition()
    {
        var info = _spriteAnimator.texture2DSheetInfo.Value;

        if (info.texture2D != null && info.cellSize != Vector2.zero)
        {
            Vector2 cellSize = info.cellSize;
            var originScaleToWidth = _originScaleToWidth;
            var originScaleToHeight = originScaleToWidth; //因为是正方形，所以用width和height相同
            float z = cellSize.y / 10f * 0.09f;
            float y = cellSize.y / originScaleToHeight; //+ _originRendererLocalPosition.y
            float rate = 0;
            if (cellSize.y < 36)
            {
                rate = cellSize.y * 0.00175f;
            }
            else
            {
                rate = cellSize.y * 0.004f;
            }
            y = y + y * rate;
            // 应用缩放（保留 z）
            transform.localPosition = new Vector3(_originRendererLocalPosition.x, y, z);
        }
    }
    // private void SetFlipX(bool flipX) {
    //     if(rendererGameObject != null) {
    //         rendererGameObject.transform.rotation.Set(flipX ? 180 : 0, 0, 0, 0);
    //     }
    // }
    // protected override void Update()
    // {
    //     base.Update();
    //     if (transform.localPosition.y == originLocalPosition.y && battleCharacterFrontSprites.Length > 0) {
    //         UpdateLocalPosition(battleCharacterFrontSprites[0]);
    //     }
    // }
    // public void IdleAnimation()
    // {
    //     // StopAnimation();
    //     _texture2DSheetAnimator.Stop();
    // }
    public async UniTask OutBallAnimation(bool animation = true)
    {
        // StopAnimation();
        // Stop();
        if (!_resourceInfoLoaded)
        {
            await LoadCharacterSprites(default);
        }
        RefreshBattlePokeContentSize();
        RefreshOriginScale();
        RefreshLocalPosition();
        // await SetSprites(battleCharacterFrontSprites);
        // Debug.Log($"放大 {SpriteResourceInfo.FullName}");
        await ScaleUp(animation);
        // RefreshContentSize();
        // Play();
        // StartAnimationIfNeed();
        // complete();
    }
    //回到球里
    public async UniTask BackBallAnimation(bool animation = true)
    {
        // PauseAnimation();
        // Pause();
        Debug.Log($"缩小 {SpriteResourceInfo.FullName}");
        await ScaleDown(animation);
    }
    /// <summary>
    /// 循环放大缩小动画，持续一定时间后还原大小
    /// </summary>
    /// <param name="startWithScaleUp">是否从放大开始</param>
    /// <param name="duration">总共循环的秒数</param>
    /// <param name="scaleMin">最小缩放比例（默认0.8）</param>
    /// <param name="scaleMax">最大放大比例（默认1.2）</param>
    /// <param name="singleCycleDuration">单次放大或缩小的时间（默认0.3s）</param>
    public async UniTask PlayPulseAnimation(
    bool startWithScaleUp,
    float duration,
    float scaleMin = 0.6f,
    float scaleMax = 1.4f,
    float singleCycleDuration = 0.3f)
    {
        if (rendererGameObject == null) return;

        rendererGameObject.transform.DOKill();

        Vector3 originalScale = _originRendererLocalScale;

        Vector3 minScale = new Vector3(
            originalScale.x * scaleMin,
            originalScale.y * scaleMin,
            originalScale.z * scaleMin
        );
        Vector3 maxScale = new Vector3(
            originalScale.x * scaleMax,
            originalScale.y * scaleMax,
            originalScale.z * scaleMax
        );

        rendererGameObject.transform.localScale = startWithScaleUp ? minScale : maxScale;

        float elapsed = 0f;
        bool scalingUp = startWithScaleUp;

        while (elapsed < duration)
        {
            Vector3 targetScale = scalingUp ? maxScale : minScale;

            await rendererGameObject.transform
                .DOScale(targetScale, singleCycleDuration)
                .SetEase(Ease.InOutSine)
                .AsyncWaitForCompletion();

            scalingUp = !scalingUp;
            elapsed += singleCycleDuration;
        }

        await rendererGameObject.transform
            .DOScale(originalScale, 0.2f)
            .SetEase(Ease.OutQuad)
            .AsyncWaitForCompletion();
    }



    public async UniTask ScaleDown(bool animation = true)
    {
        if (rendererGameObject != null)
        {
            StopFading();

            // 使用 DOTween 缩小到 0.2 倍大小
            await rendererGameObject.transform.DOScale(0.0f, animation ? 0.5f : 0.0f) // 0.5秒完成缩小
                .SetEase(Ease.InOutQuad).AsyncWaitForCompletion(); // 使用缓入缓出的平滑效果
            // 渐变透明度到 80%（Alpha = 0.8）
            // spriteRenderer.DOFade(0.8f, 0.5f) // 0.5秒完成透明度渐变
            //     .SetEase(Ease.InOutQuad);
        }
    }
    // public void PauseAnimation(){
    //     _texture2DSheetAnimator.Pause();
    // }
    // public void ResumeAnimation(){
    //     _texture2DSheetAnimator.Play();
    // }
    public async UniTask ScaleUp(bool animation = true)
    {
        if (rendererGameObject != null)
        {

            StopFading();
            // 确保初始状态为缩小状态
            rendererGameObject.transform.localScale = Vector3.one * 0.2f;

            // 使用 DOTween 放大到 3 倍大小
            await rendererGameObject.transform.DOScale(_originRendererLocalScale, animation ? 0.5f : 0.0f) // 0.5秒完成放大
                .SetEase(Ease.OutBounce).AsyncWaitForCompletion(); // 使用弹性放大效果
            // 渐变透明度到不透明（Alpha = 1.0）
            // rendererGameObject.DOFade(1f, 0.5f) // 0.5秒完成透明度渐变
            //     .SetEase(Ease.InOutQuad);
        }
    }
    // private async UniTask LoadCharacterSprites()
    // {
    //     if (SpriteResourceInfo == null)
    //     {
    //         return;
    //     }
    //     var texture2D = await SpriteResourceInfo.LoadAnimationTexture2D();
    //     if(texture2D.Success) {
    //         _texture2DSheetAnimator.GetLitMaterial(texture2D.Content.texture2D, texture2D.Content.rows, texture2D.Content.columns);

    //     } else {
    //         Debug.LogError($"LoadCharacterSprites 加载失败 {SpriteResourceInfo.FullName}");
    //     }
    //     throw new NotImplementedException();
    //     // StartCoroutine(SpriteResourceInfo.LoadAnimationSprites((result) =>
    //     // {
    //     //     StopAnimation();//加载完成要切换资源，所以停止动画

    //     //     if (result.Result != null)
    //     //     {
    //     //         if (result.Result.Count > 0)
    //     //         {
    //     //             Sprite sprite = result.Result[0].FirstOrDefault();
    //     //             UpdateLocalPosition(sprite);
    //     //             battleCharacterFrontSprites = result.Result[0]; //只进行动画的加载
    //     //             complete();
    //     //             Debug.Log($"LoadCharacterSprites 位置计算 执行完成 {SpriteResourceInfo.FullName}");
    //     //         }
    //     //     }
    //     // }));
    // }
    private void UpdateLocalPosition(Sprite sprite)
    {

        float spriteWidthPixels = sprite.rect.width;
        float spriteHeightPixels = sprite.rect.height;

        // 获取 Sprite 的 Pixels Per Unit
        float pixelsPerUnit = sprite.pixelsPerUnit;

        // 获取 Transform 的缩放比例
        Vector3 localScale = _originRendererLocalScale;

        // 计算 Sprite 在 3D 世界中的宽高
        float worldWidth = (spriteWidthPixels / pixelsPerUnit) * localScale.x;
        float worldHeight = (spriteHeightPixels / pixelsPerUnit) * localScale.y;

        // 定义点 A 和点 B 的坐标
        float xA = 0, yA = 0; // A点 (旋转中心)
        float xB = worldHeight, yB = 0; // B点 (需要旋转的点)
        float theta = 30; // 旋转角度（度数）

        // 调用计算偏移量的方法
        (float deltaX, float deltaY) = CalculateOffset(xA, yA, xB, yB, theta);
        // float worldWidth = (spriteWidthPixels / pixelsPerUnit);
        // float worldHeight = (spriteHeightPixels / pixelsPerUnit);
        // var position = this.transform.position;
        Vector3 originPosition = transform.localPosition;
        //y默认是0
        originPosition.y = worldHeight / 2 + deltaX; // 例如，调整到顶部对齐
        transform.localPosition = originPosition;


        // originPosition = transform.position;
        // SetSprites(result.Result[0]);
    }
    public static (float, float) CalculateOffset(float xA, float yA, float xB, float yB, float theta)
    {
        // 将角度转换为弧度
        double radians = theta * Math.PI / 180.0;

        // 计算原点相对位置
        double relativeX = xB - xA;
        double relativeY = yB - yA;

        // 计算旋转后的点的坐标
        double xBPrime = xA + (relativeX * Math.Cos(radians) - relativeY * Math.Sin(radians));
        double yBPrime = yA + (relativeX * Math.Sin(radians) + relativeY * Math.Cos(radians));

        // 计算偏移量
        double deltaX = xBPrime - xB;
        double deltaY = yBPrime - yB;

        return ((float)deltaX, (float)deltaY);
    }
    public void ChangeDirectionIfNeed(Direction direction, bool farce = false)
    {
        // var animatorDirection = AnimatorDirectionByDirection(direction);
        // if (farce || currentAnimatorDirection != animatorDirection)
        // {
        //     SetCurrentAnimatorDirection(animatorDirection);
        // }
    }
    //看后续会不会添加其他的
    // public void ChangeAnimationIfNeed(PlayerAnimations animation, bool farce = false)
    // {
    //     // if(farce) {
    //     //     SetCurrentAnimatorDirection()
    //     // }
    // }

    public void Fade(Action complete)
    {
        // if (spriteRenderer == null)
        // {
        //     Debug.LogError("SpriteRenderer is not assigned.");
        //     complete?.Invoke(); // 确保即使 spriteRenderer 为 null 也会调用回调
        //     return;
        // }

        // float disappearDuration = 0.1f;
        // float reappearDuration = 0.1f;

        // // 确保 DOTween 动画不会堆积
        // // spriteRenderer.DOKill();
        // StopFading();

        // // 逐渐消失
        // spriteRenderer.DOFade(0, disappearDuration).OnComplete(() =>
        // {
        //     // 逐渐重新出现
        //     spriteRenderer.DOFade(1, reappearDuration).OnComplete(() =>
        //     {
        //         // 动画完成后执行回调
        //         complete?.Invoke();
        //     });
        // });
    }

    public void StopFading()
    {
        rendererGameObject.transform.DOComplete(); // 立即完成当前动画，设置到最终状态
        rendererGameObject.transform.DOKill(); // 停止所有相关动画
        // spriteRenderer.DOComplete(); // 立即完成当前动画，设置到最终状态
        // spriteRenderer.DOKill(); // 停止所有相关动画
    }
}