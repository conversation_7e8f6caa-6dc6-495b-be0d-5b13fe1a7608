using Cysharp.Threading.Tasks;
using UnityEngine;
using DG.Tweening;
public class NinTeraAnimator : MonoBehaviour
{
    public Transform circleTransform;
    public Transform effectPrefab;
    public Transform imagePrefab; //太晶的logo
    public async UniTask PlayEffect() {
        effectPrefab.gameObject.SetActive(true);
        circleTransform.gameObject.SetActive(true);
        imagePrefab.gameObject.SetActive(true);
        RestartParticles(effectPrefab);
        StopFading(circleTransform);
        //让imagePrefab 对着镜头
        imagePrefab.LookAt(Camera.main.transform);
        imagePrefab.localScale = Vector3.one * 0.0f;
        await UniTask.Delay(300);
        imagePrefab.DOScale(Vector3.one * 4, 0.5f).SetEase(Ease.OutBounce).onComplete = () => {
            imagePrefab.gameObject.SetActive(false);
        };
        await UniTask.Delay(300);
        circleTransform.localScale = Vector3.one * 0.1f;
        circleTransform.gameObject.SetActive(true);
        await circleTransform.DOScale(Vector3.one * 1.3f, 0.6f).SetEase(Ease.OutBounce).AsyncWaitForCompletion();
        await UniTask.Delay(600);
        circleTransform.gameObject.SetActive(false);
        await UniTask.Delay(100);
        effectPrefab.gameObject.SetActive(false);
    }
    private void RestartParticles(Transform prefabRoot)
    {
        ParticleSystem[] particles = prefabRoot.GetComponentsInChildren<ParticleSystem>();
        foreach (var ps in particles)
        {
            ps.Clear();
            ps.Play();
        }
    }
    public void StopFading(Transform transform)
    {
        transform.DOComplete(); // 立即完成当前动画，设置到最终状态
        transform.DOKill(); // 停止所有相关动画
        // spriteRenderer.DOComplete(); // 立即完成当前动画，设置到最终状态
        // spriteRenderer.DOKill(); // 停止所有相关动画
    }
}