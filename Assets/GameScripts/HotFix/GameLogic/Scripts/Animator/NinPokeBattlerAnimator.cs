using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
using System;
using PokeApiNet;
using PsBattleModel;
using Cysharp.Threading.Tasks;
using System.Threading.Tasks;
//战场中的精灵
public class NinPokeBattlerAnimator : MonoBehaviour
{
    //精灵
    public NinBattleCharacterAnimator pokeAnimator;
    //球
    public NinBattleBall battleBall;
    //特效
    public GameObject shinyEffect;
    public NinMegaAnimator megaEffect;
    public NinTeraAnimator teraAnimator;
    public NinZmoveAnimator zmoveAnimator;
    public NinMaxAnimator maxAnimator;
    // public NinBattleCharacterAnimator EffectAnimator;
    //model
    public IBattlePoke? ninBattlerModel;
    public bool isLeft = true;
    public async UniTask Fainted() {
        if(ninBattlerModel != null) {
            await PokemonBackBall();
            // PokemonBackBall(() => {
            //     complete();
            // });
        }
        //  else {
        //     complete();
        // }
    }
    public void Clear() {
        ninBattlerModel = null;
        pokeAnimator.Clear();
    }
    public void SetOnDirection(bool isLeft)
    {
        battleBall.IsLeft = isLeft;
        this.isLeft = isLeft;
        // if(NinSettingConfig.Instance.ClassicCamera) {
        //     pokeAnimator.flipX = false;
        // } else {
        pokeAnimator.flipX = isLeft;
        // }
    }
    public async UniTask FastShowPokemon(IBattlePoke ninBattlerModel, Action complete) {
        var SpriteDirection = PokemonResourceConditions.BattlerFront;
        if(NinSettingConfig.Instance.ClassicCamera && isLeft) {
            SpriteDirection = PokemonResourceConditions.BattlerBack;
        }
        PokemonResourceInfo info = new(ninBattlerModel, SpriteDirection);
        await pokeAnimator.SetSpriteResourceInfo(info);
        await pokeAnimator.OutBallAnimation(false);
        complete();
    }
    public async UniTask SwitchPokemon(IBattlePoke ninBattlerModel, Action complete)
    {
        // var localPokemonData = ninBattlerModel.LocalPokemonData();
        // Debug.Log($"SwitchPokemon ninBattlerModel:{ninBattlerModel.PokeIdInfo.siden}, PokemonOutBall:{ninBattlerModel.pokemonSpecies.NameLocalized}");
        var SpriteDirection = PokemonResourceConditions.BattlerFront;
        if(NinSettingConfig.Instance.ClassicCamera && isLeft) {
            SpriteDirection = PokemonResourceConditions.BattlerBack;
        }
        PokemonResourceInfo info = new(ninBattlerModel, SpriteDirection);
        // info.Pokemon = ninBattlerModel.localPokemonData;
        // info.Conditions = PokemonResourceConditions.BattlerFront;

        // 标记是否加载成功和动画完成
        bool isSpriteLoaded = false;
        bool isAnimationComplete = false;

        // 判断是否可以调用 PokemonOutBall
        async UniTask TryComplete()
        {
            if (isSpriteLoaded && isAnimationComplete)
            {
                await PokemonOutBall();
                if(ninBattlerModel.shiny > 0) {//闪
                    ShowShinyEffect();
                    await UniTask.Delay(100);
                }
                Debug.Log($"switch 动画完成");
                complete();
            }
        }

        if (this.ninBattlerModel != null)
        {
            await PokemonBackBall();
        }

        this.ninBattlerModel = ninBattlerModel;
        //先加载球
        await battleBall.ConfigBallName(ninBattlerModel.pokeball);
        battleBall.StartParabolic(async () =>
        {
            isAnimationComplete = true; // 标记动画已完成
            await TryComplete();              // 尝试完成
        }).Forget();
        //前面是扔球的动画，这时同步进行图片加载
        await pokeAnimator.SetSpriteResourceInfo(info);
        //先隐藏不可见
        await pokeAnimator.BackBallAnimation(false);
        isSpriteLoaded = true; // 标记图片已加载
        await TryComplete(); // 尝试完成
    }
    public async UniTask SetPokeInfo(PokemonResourceInfo info) {
        await pokeAnimator.SetSpriteResourceInfo(info);
    }
    public async UniTask PlayEvolution(bool evolved) {
        await pokeAnimator.EvolutionAnimation(evolved);
    }
    public async UniTask PlayMega() {
        megaEffect.PlayEffect().Forget();
        await UniTask.Delay(1500);
        await pokeAnimator.ScaleDown(false);
        await UniTask.Delay(500);
        await FastShowPokemon(ninBattlerModel, () => { });
        await UniTask.Delay(400);
        // megaEffect.SetActive(false);
        // await pokeAnimator.EvolutionAnimation(true);
    }
    public async UniTask PlayTera(NinBattlePokeTypeModel pokeType) {
        teraAnimator.PlayEffect().Forget();
        await UniTask.Delay(900);
        await pokeAnimator.ScaleDown(false);
        await UniTask.Delay(200);
        await FastShowPokemon(ninBattlerModel, () => { });
        await pokeAnimator.SetTeraEffect(pokeType);
        await UniTask.Delay(1000); 
    }
    public async UniTask PlayZmove() {
        await zmoveAnimator.PlayEffect();
    }
    public async UniTask PlayMax() {
        await maxAnimator.PlayEffect();
    }
    public async UniTask StopMaxEffect() {
        //缩小动画
        maxAnimator.StopEffect();
    }
    async UniTask PokemonBackBall() {
        await pokeAnimator.BackBallAnimation();
        // PokeAnimator.BackBallAnimation(() =>
        // {
        //     // 退球动画完成逻辑（如果需要，可填充）
        //     complete();
        // }); 
    }
    async UniTask PokemonOutBall()
    {
        // var pokemonSpecies = await this.ninBattlerModel.PokemonSpecies();
        //  Debug.Log($"PokemonOutBall {pokemonSpecies.NameLocalized}");
        battleBall.Dismiss();
        await pokeAnimator.OutBallAnimation();
        // PokeAnimator.OutBallAnimation(() =>
        // {
            
        //     if(shiny) {//闪
        //         ShowShinyEffect();
        //     }
        //     //球里出来
        //     complete();
        // });
    }
    void ShowShinyEffect()
    {
        // 确保 ShinyEffect 被激活
        shinyEffect.SetActive(true);

        // 获取粒子系统组件
        var ps = shinyEffect.GetComponent<ParticleSystem>();

        if (ps != null)
        {
            // 停止并清除粒子系统，然后重新播放
            ps.Stop();
            ps.Clear();
            ps.Play();
        }
        else
        {
            Debug.LogWarning("ParticleSystem component not found on ShinyEffect.");
        }
    }
    public void UnderAttack(Action complete)
    {
        pokeAnimator.ShakePosition();
        complete();
    }
    public void UseMove(Action complete)
    {
        pokeAnimator.UseMove(() =>
        {
            complete();
            BackOriginPosition(true, () => { });
        });
    }
    public void BackOriginPosition(bool animation, Action complete)
    {
        pokeAnimator.ResetLocalPosition(animation, complete);
    }
    public void UpdateFaceCamera() {
        if(Camera.main != null) {
            pokeAnimator.ninBattleCharacterSpriteAnimator.transform.LookAt(Camera.main.transform);
            battleBall.transform.LookAt(Camera.main.transform);
        }
        // var localRotation = pokeAnimator.ninBattleCharacterSpriteAnimator.transform.localRotation;
        // var eulerAngles = localRotation.eulerAngles;
        // if(NinSettingConfig.Instance.ClassicCamera) {
        //     if(isLeft) {
        //         eulerAngles.x = 30;
        //         eulerAngles.y = 50;
        //     } else {
        //         eulerAngles.x = 20;
        //         eulerAngles.y = 80;
        //     }
        // } else {
        //     eulerAngles.x = 30;
        //     eulerAngles.y = 0;
        // }
        // localRotation.eulerAngles = eulerAngles;
        // // localRotation.y = 70;
        // pokeAnimator.ninBattleCharacterSpriteAnimator.transform.localRotation = localRotation;
        // battleBall.transform.localRotation = localRotation;

    }
    void Start()
    {
        // this.Position = new(0,15,0);
        // this.character.transform.position = new(0,15,0);
        // characterAnimator.ChangeAnimation(PlayerAnimations.Run);
    }

    // Update is called once per frame
    void Update()
    {
        UpdateFaceCamera();
        // this.character.transform.position = new(0,15,0);
        // Debug.Log("BattlePetsRole: "+ this.character.transform.position);
    }

}
