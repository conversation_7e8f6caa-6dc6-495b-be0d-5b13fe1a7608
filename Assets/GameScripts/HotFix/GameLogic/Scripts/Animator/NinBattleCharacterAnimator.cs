using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Role;
using DG.Tweening;
using UnityEngine.UIElements;
using PokeApiNet;
using System.Linq;
using System;
using Cysharp.Threading.Tasks;
using System.Threading.Tasks;

// 角色方向枚举
// public enum NinCharacterAnimatorDirection
// {
//     Down,
//     Left,
//     Right,
//     Up
// }

public class NinBattleCharacterAnimator: MonoBehaviour
{
    public NinBattleCharacterSpriteAnimator ninBattleCharacterSpriteAnimator;
    private Vector3 originLocalPosition;
    private float _moveDuration = 0.3f; //出招式前进的时间
    // private Vector3 originLocalScale;
    // private Sprite[] currentCharacterSprites = new Sprite[0];
    public string characterName; // 角色名称（用于加载特定的角色图片）
    // public bool battle; //是否是战斗图
    private IAnimationSpriteResourceInfo _spriteResourceInfo;
    public IAnimationSpriteResourceInfo SpriteResourceInfo {
        get {
            return _spriteResourceInfo;
        }
        // set {
        //     _spriteResourceInfo = value;
        //     LoadCharacterSprites();
        // }
    }
    private bool _flipX = false;
    public bool flipX {
        get {
            return _flipX;
        }
        set {
            _flipX = value;
            ninBattleCharacterSpriteAnimator.flipX = value;
        }
    }
    //图片是否加载完成
    public async UniTask SetSpriteResourceInfo(IAnimationSpriteResourceInfo info) {
        _spriteResourceInfo = info;
        await ninBattleCharacterSpriteAnimator.SetSpriteResourceInfo(info);
        flipX = flipX; // 设置方向
        // LoadCharacterSprites(complete);
    }

    void Start()
    {
        originLocalPosition = transform.localPosition;
    }
    public async UniTask SetTeraEffect(NinBattlePokeTypeModel pokeType) {
        // ninBattleCharacterSpriteAnimator.SetTeraEffect(null);
        if(pokeType.pokeType == MainServer.PokeTypeEnum.PokeTypeUnknown) {
            ninBattleCharacterSpriteAnimator.SetTeraEffect(null);
        } else {
            Texture2D texture = await DefaultResourceInfo.GetTeraPattern(pokeType.NameId, default);
            ninBattleCharacterSpriteAnimator.SetTeraEffect(texture);
        }
    } 
    public async UniTask OutBallAnimation(bool animation = true) {
        ninBattleCharacterSpriteAnimator.Stop();
        await ninBattleCharacterSpriteAnimator.OutBallAnimation(animation);
        ninBattleCharacterSpriteAnimator.Play();
    }
    //回到球里
    public async UniTask BackBallAnimation(bool animation = true) {
        ninBattleCharacterSpriteAnimator.Pause();
        await ninBattleCharacterSpriteAnimator.BackBallAnimation(animation);
    }
    public async UniTask EvolutionAnimation(bool evolved) {//播放的是进化后的精灵动画
        ninBattleCharacterSpriteAnimator.Pause();
        await ninBattleCharacterSpriteAnimator.PlayPulseAnimation(evolved, 3f);
        ninBattleCharacterSpriteAnimator.Play();
    }
    public async UniTask ScaleDown(bool animation = true) {
        ninBattleCharacterSpriteAnimator.Pause();
        await ninBattleCharacterSpriteAnimator.BackBallAnimation(animation);
    }
    public async UniTask ScaleUp(bool animation = true) {
        ninBattleCharacterSpriteAnimator.Pause();
        await ninBattleCharacterSpriteAnimator.OutBallAnimation(animation);
    }
    public void UseMove(Action complete) {

        float forwardDistance = -3f;  // 向前移动的距离
        if(_flipX) { //方向
            forwardDistance = 3f;
        }
        // 保存初始位置
        // Vector3 originalPosition = transform.position;

        // 计算前方目标位置
        Vector3 forwardPosition = originLocalPosition;
        forwardPosition.x = forwardPosition.x + forwardDistance;
        StopFading();
        // 移到目标位置
        transform.DOLocalMove(forwardPosition, _moveDuration)
            .SetEase(Ease.OutQuad) // 可选：设置缓动效果
            .OnComplete(() => // 在前移完成后回到原位
            {
                complete();
            });
    }
    public void ResetLocalPosition(bool animation, Action complete) {
        if(animation) {
            StopFading();
            transform.DOLocalMove(originLocalPosition, _moveDuration)
            .SetEase(Ease.InQuad) // 可选：设置返回的缓动效果
            .OnComplete(() => {
                complete();
            });
        } else {
            complete();
        }
    }
    //受机动画
    public void ShakePosition() {
        StopFading();
        ninBattleCharacterSpriteAnimator.Pause();
        transform.DOShakePosition(0.5f, 0.5f).OnComplete(() => {
            ninBattleCharacterSpriteAnimator.Play();
        });
    }
    /// <summary>
    /// 躲避技能：先消失再出现
    /// </summary>
    public void Avoid(Action complete)
    {
        ninBattleCharacterSpriteAnimator.Fade(complete);
    }
    public void StopFading() {
        transform.DOComplete(); // 立即完成当前动画，设置到最终状态
        transform.DOKill(); // 停止所有相关动画
    }
    public void Clear() {
        _spriteResourceInfo = null;
        ninBattleCharacterSpriteAnimator.Clear();
    }
}