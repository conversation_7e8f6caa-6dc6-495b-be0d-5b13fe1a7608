using UnityEngine;
using UnityEngine.UI;
public class MapChatacterOprationUI : MonoBehaviour
{
    public NinSimpleButton CenterBtn;
    public NinSimpleButton LeftBtn;
    public NinSimpleButton RightBtn;
    public NinSimpleButton UpBtn;
    public NinSimpleButton DownBtn;
    public bool _onWater;
    
    private void Awake() {
        CenterBtn.clickedAction = () => {
            Hide();
        };
        LeftBtn.clickedAction = () => {
            LeftBenClicked();
        };
        RightBtn.clickedAction = () => {
            RightBtnClicked();
        };
        UpBtn.clickedAction = () => {
            UpBtnClicked();
        };
        DownBtn.clickedAction = () => {
            DownBtnClicked();
        };
    }
    public void LeftBenClicked() {
        // Hide();
        Debug.Log("LeftBenClicked");
        if(_onWater) {
            MapController.Current.mapCharacterMgr.myCharacter.MoveToWaterEdgeLand();
        } else {
            MapController.Current.mapCharacterMgr.myCharacter.TryMoveToNavigableWaterCell();
        }
        Hide();
    }
    public void RightBtnClicked() {
        // Hide();
        Debug.Log("RightBtnClicked");
        MapController.Current.mapCharacterMgr.myCharacter.TryMoveToNavigableWaterCell();
        Hide();
    }
    public void UpBtnClicked() {
        Hide();
    }
    public void DownBtnClicked() {
        Hide();
    }
    // public void Show(NinChatacterOprationTip.OprationType opration) {

    //     switch (opration)
    //     {
    //         case NinChatacterOprationTip.OprationType.Water:
    //             _onWater = true;
    //             CenterBtn.gameObject.SetActive(true);
    //             LeftBtn.gameObject.SetActive(true);
    //             RightBtn.gameObject.SetActive(false);
    //             UpBtn.gameObject.SetActive(false);
    //             DownBtn.gameObject.SetActive(false);
    //             break;
    //         case NinChatacterOprationTip.OprationType.OnWaterFaceLand:
    //             _onWater = true;
    //             CenterBtn.gameObject.SetActive(true);
    //             LeftBtn.gameObject.SetActive(true);
    //             RightBtn.gameObject.SetActive(false);
    //             UpBtn.gameObject.SetActive(true);
    //             DownBtn.gameObject.SetActive(true);
    //             break;
    //         case NinChatacterOprationTip.OprationType.OnLandFaceWater:
    //             _onWater = false;
    //             CenterBtn.gameObject.SetActive(true);
    //             LeftBtn.gameObject.SetActive(false);
    //             RightBtn.gameObject.SetActive(true);
    //             UpBtn.gameObject.SetActive(false);
    //             DownBtn.gameObject.SetActive(true);
    //             break;
    //         case NinChatacterOprationTip.OprationType.None:
    //             _onWater = false;
    //             Hide();
    //             break;
    //     }
    //     this.gameObject.SetActive(true);
    // }
    public void Hide() {
        this.gameObject.SetActive(false);
    }
}