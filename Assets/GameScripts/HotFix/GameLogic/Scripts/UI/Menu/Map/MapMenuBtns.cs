using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UI;

public class MapMenuBtns : MonoBehaviour
{
    public enum BtnType {
        Menu,
        Match,
        Chat,
        Information,
        GroupInfo,
        Map,
        Fishing,
        Waves,
        MoveTeleport
    }

    public enum RightBottomOprationType {
        None,
        // Water,
        OnWater,
        OnLand,
        FaceWater, // 面向水面（钓鱼
    }
    public Action<BtnType> BtnClickAction;
    [Header("左上角")]
    public Button ChatBtn;
    public Button MatchBtn;
    public Button groupInfoBtn; //队伍按钮
    [Header("右上角")]
    public Button MenuBtn;
    public Button InformationBtn; //包括任务，还有活动
    [Header("右下角")]
    public Button mapBtn; //地图
    public Button fishingBtn; //钓鱼
    public Button wavesBtn; //冲浪
    public Button moveTeleportBtn; //移动
    void Start()
    {
        if(MenuBtn != null) {
            MenuBtn.onClick.AddListener(() => {
                BtnClickAction?.Invoke(BtnType.Menu);
            });
        }
        if(MatchBtn != null) {
            MatchBtn.onClick.AddListener(() => {
                BtnClickAction?.Invoke(BtnType.Match);
            });
        }
        if(ChatBtn != null) {
            ChatBtn.onClick.AddListener(() => {
                BtnClickAction?.Invoke(BtnType.Chat);
            });
        }
        if(InformationBtn != null) {
            InformationBtn.onClick.AddListener(() => {
                BtnClickAction?.Invoke(BtnType.Information);
            });
        }
        if(groupInfoBtn != null) {
            groupInfoBtn.onClick.AddListener(() => {
                BtnClickAction?.Invoke(BtnType.GroupInfo);
            });
        }
        if(mapBtn != null) {
            mapBtn.onClick.AddListener(() => {
                BtnClickAction?.Invoke(BtnType.Map);
            });
        }
        if(fishingBtn != null) {
            fishingBtn.onClick.AddListener(() => {
                BtnClickAction?.Invoke(BtnType.Fishing);
            });
        }
        if(wavesBtn != null) {
            wavesBtn.onClick.AddListener(() => {
                BtnClickAction?.Invoke(BtnType.Waves);
            });
        }
        if(moveTeleportBtn != null) {
            moveTeleportBtn.onClick.AddListener(() => {
                BtnClickAction?.Invoke(BtnType.MoveTeleport);
            });
        }
    }
    public void SetRightBottomOprationType(List<RightBottomOprationType> types) {
        fishingBtn.gameObject.SetActive(false);
        wavesBtn.gameObject.SetActive(false);
        moveTeleportBtn.gameObject.SetActive(false);
        if(types.Contains(RightBottomOprationType.OnLand) && types.Contains(RightBottomOprationType.FaceWater)) {
            wavesBtn.gameObject.SetActive(true);
        }
        if(types.Contains(RightBottomOprationType.FaceWater)) {
            fishingBtn.gameObject.SetActive(true);
        }
        
        // switch (type)
        // {
        //     case RightBottomOprationType.None:
        //         break;
        //     case RightBottomOprationType.Water:
        //         break;
        //     case RightBottomOprationType.OnWaterFaceLand:
        //         break;
        //     case RightBottomOprationType.OnLandFaceWater:
        //         moveTeleportBtn.gameObject.SetActive(false);
        //         break;
        // }
    }
}