using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using PetsServices.Battle;
using System.Linq;
using PsBattleModel;
using System.Threading.Tasks;

//战斗管理，包括Field还有Pets
public class BattleMinorActionExecutor : BaseBattleActionExecutor<MinorActionParser, IMinorActionExecutor>, IMinorActionExecutor
{
    BattleActionExecutor mainActionExecutor;
    public BattleMinorActionExecutor(BattleActionExecutor executor) {
        mainActionExecutor = executor;
        MainBattleActionExecutor = executor;
    }
// public async Task DamagePokemon()
    // {
    //     string? pokemon = ExecutingUnit.NextValue();
    //     string? hpStatus = ExecutingUnit.NextValue();
    //     mainActionExecutor.battleContext.UpdateBattlePokeHpStatus(pokemon, hpStatus);
    //     ExecutingUnit.TryEndExecute();
    // }
    // public async Task HealPokemon(string pokemon, string hpStatus, string? from)
    // {
    //     // throw new System.NotImplementedException();
    //     Debug.Log($"HealPokemon {pokemon} {hpStatus}");
    //     mainActionExecutor.battleContext.UpdateBattlePokeHpStatus(pokemon, hpStatus);
    //     ExecutingUnit.TryEndExecute();
    // }
    // public async Task SetHp(string pokemon, string hp)
    // {
    //     Debug.Log($"SetHp {pokemon} {hp}");
    //     throw new System.NotImplementedException();
    // }
    // public async Task BoostStat(string pokemon, string stat, int amount)
    // {
    //     mainActionExecutor.ShowMessage($"{pokemon}{stat}能力上升{amount}");
    //     int oriBoost = mainActionExecutor.battleContext.GetPokeBoost(pokemon, stat);
    //     mainActionExecutor.battleContext.SetBoost(pokemon, stat, oriBoost + amount);

    //     ExecutingUnit.TryEndExecute();
    // }
    // public async Task UnboostStat(string pokemon, string stat, int amount)
    // {
    //     mainActionExecutor.ShowMessage($"{pokemon}{stat}能力下降{amount}");
    //     int oriBoost = mainActionExecutor.battleContext.GetPokeBoost(pokemon, stat);
    //     mainActionExecutor.battleContext.SetBoost(pokemon, stat, oriBoost - amount);

    //     ExecutingUnit.TryEndExecute();
    // }
    public async Task Damage(BattleMessageUnit unit)
    {
        string? pokemon = unit.NextValue();
        string? hpStatus = unit.NextValue();
        if(string.IsNullOrEmpty(pokemon) || string.IsNullOrEmpty(hpStatus)) {
            Debug.LogError($"DamagePokemon null {pokemon} {hpStatus}");
            unit.TryEndExecute();
            return;
        }
        mainActionExecutor.battleContext.UpdateBattlePokeHpStatus(pokemon, hpStatus);
        unit.TryEndExecute();
    }

    public async Task Heal(BattleMessageUnit unit)
    {
        string? pokemon = unit.NextValue();
        string? hpStatus = unit.NextValue();
        if(string.IsNullOrEmpty(pokemon) || string.IsNullOrEmpty(hpStatus)) {
            Debug.LogError($"HealPokemon null {pokemon} {hpStatus}");
            unit.TryEndExecute();
            return;
        }
        mainActionExecutor.battleContext.UpdateBattlePokeHpStatus(pokemon, hpStatus);
        unit.TryEndExecute();
    }

    public async Task Sethp(BattleMessageUnit unit)
    {
        string? pokemon = unit.NextValue();
        string? hp = unit.NextValue();
        Debug.Log($"SetHp {pokemon} {hp}");
        throw new System.NotImplementedException();
    }

    public async Task Boost(BattleMessageUnit unit)
    {
        string? pokemon = unit.NextValue();
        string? stat = unit.NextValue();
        string? amountStr = unit.NextValue();
        if(string.IsNullOrEmpty(pokemon) || string.IsNullOrEmpty(stat) || string.IsNullOrEmpty(amountStr)) {
            Debug.LogError($"Boost null {pokemon} {stat} {amountStr}");
            unit.TryEndExecute();
            return;
        }
        if (!int.TryParse(amountStr, out int amount))
        {
            Debug.LogError($"Boost amount parse error {amountStr}");
            unit.TryEndExecute();
            return;
        }
        mainActionExecutor.ShowMessage($"{pokemon}{stat}能力上升{amount}");
        int oriBoost = mainActionExecutor.battleContext.GetPokeBoost(pokemon, stat);
        mainActionExecutor.battleContext.SetBoost(pokemon, stat, oriBoost + amount);
        unit.TryEndExecute();
    }

    public async Task Unboost(BattleMessageUnit unit)
    {       string? pokemon = unit.NextValue();
        string? stat = unit.NextValue();
        string? amountStr = unit.NextValue();
        if(string.IsNullOrEmpty(pokemon) || string.IsNullOrEmpty(stat) || string.IsNullOrEmpty(amountStr)) {
            Debug.LogError($"Boost null {pokemon} {stat} {amountStr}");
            unit.TryEndExecute();
            return;
        }
        if (!int.TryParse(amountStr, out int amount))
        {
            Debug.LogError($"Boost amount parse error {amountStr}");
            unit.TryEndExecute();
            return;
        }
        mainActionExecutor.ShowMessage($"{pokemon}{stat}能力下降{amount}");
        int oriBoost = mainActionExecutor.battleContext.GetPokeBoost(pokemon, stat);
        mainActionExecutor.battleContext.SetBoost(pokemon, stat, oriBoost - amount);
        unit.TryEndExecute();
    }

    public async Task Setboost(BattleMessageUnit unit)
    {
        string? pokemon = unit.NextValue();
        string? stat = unit.NextValue();
        string? amountStr = unit.NextValue();
        if(string.IsNullOrEmpty(pokemon) || string.IsNullOrEmpty(stat) || string.IsNullOrEmpty(amountStr)) {
            Debug.LogError($"Boost null {pokemon} {stat} {amountStr}");
            unit.TryEndExecute();
            return;
        }
        if (!int.TryParse(amountStr, out int amount))
        {
            Debug.LogError($"Boost amount parse error {amountStr}");
            unit.TryEndExecute();
            return;
        }
        mainActionExecutor.battleContext.SetBoost(pokemon, stat, amount);
        unit.TryEndExecute();
    }

    public async Task Swapboost(BattleMessageUnit unit)
    {
        string? source = unit.NextValue();
        string? target = unit.NextValue();
        string? stats = unit.NextValue();
        if(string.IsNullOrEmpty(source) || string.IsNullOrEmpty(target) || string.IsNullOrEmpty(stats)) {
            Debug.LogError($"Swapboost null {source} {target} {stats}");
            unit.TryEndExecute();
            return;
        }
        throw new System.NotImplementedException();
    }

    public async Task Clearpositiveboost(BattleMessageUnit unit)
    {
        string? pokemon = unit.NextValue();
        if(string.IsNullOrEmpty(pokemon)) {
            Debug.LogError($"Clearpositiveboost null {pokemon}");
            unit.TryEndExecute();
            return;
        }
        var battlerModel = mainActionExecutor.battleContext.GetPokeBy(pokemon);
        if(battlerModel.BoostInfo.Attack > 0) {
            battlerModel.BoostInfo.Attack = 0;
        }
        if(battlerModel.BoostInfo.Defense > 0) {
            battlerModel.BoostInfo.Defense = 0;
        }
        if(battlerModel.BoostInfo.SpecialAttack > 0) {
            battlerModel.BoostInfo.SpecialAttack = 0;
        }
        if(battlerModel.BoostInfo.SpecialDefense > 0) {
            battlerModel.BoostInfo.SpecialDefense = 0;
        }
        if(battlerModel.BoostInfo.Speed > 0) {
            battlerModel.BoostInfo.Speed = 0;
        }
        if(battlerModel.BoostInfo.Accuracy > 0) {
            battlerModel.BoostInfo.Accuracy = 0;
        }
        if(battlerModel.BoostInfo.Evasion > 0) {
            battlerModel.BoostInfo.Evasion = 0;
        }
        mainActionExecutor.ShowMessage($"{battlerModel.NameLocalized} 能力被还原了");
        unit.TryEndExecute();
    }

    public async Task Clearnegativeboost(BattleMessageUnit unit)
    {
        string? pokemon = unit.NextValue();
        if(string.IsNullOrEmpty(pokemon)) {
            Debug.LogError($"Clearnegativeboost null {pokemon}");
            unit.TryEndExecute();
            return;
        }
        var battlerModel = mainActionExecutor.battleContext.GetPokeBy(pokemon);
        if(battlerModel.BoostInfo.Attack < 0) {
            battlerModel.BoostInfo.Attack = 0;
        }
        if(battlerModel.BoostInfo.Defense < 0) {
            battlerModel.BoostInfo.Defense = 0;
        }
        if(battlerModel.BoostInfo.SpecialAttack < 0) {
            battlerModel.BoostInfo.SpecialAttack = 0;
        }
        if(battlerModel.BoostInfo.SpecialDefense < 0) {
            battlerModel.BoostInfo.SpecialDefense = 0;
        }
        if(battlerModel.BoostInfo.Speed < 0) {
            battlerModel.BoostInfo.Speed = 0;
        }
        if(battlerModel.BoostInfo.Accuracy < 0) {
            battlerModel.BoostInfo.Accuracy = 0;
        }
        if(battlerModel.BoostInfo.Evasion < 0) {
            battlerModel.BoostInfo.Evasion = 0;
        }
        mainActionExecutor.ShowMessage($"{battlerModel.NameLocalized} 能力恢复了");
        unit.TryEndExecute();
    }

    public async Task Copyboost(BattleMessageUnit unit)
    {
        throw new System.NotImplementedException();
    }

    public async Task Clearboost(BattleMessageUnit unit)
    {
        string? pokemon = unit.NextValue();
        if(string.IsNullOrEmpty(pokemon)) {
            Debug.LogError($"Clearboost null {pokemon}");
            unit.TryEndExecute();
            return;
        }
        var battlerModel = mainActionExecutor.battleContext.GetPokeBy(pokemon);
        battlerModel.ClearAllBoosts();
        unit.TryEndExecute();
    }

    public async Task Invertboost(BattleMessageUnit unit)
    {
        string? pokemon = unit.NextValue();
        if(string.IsNullOrEmpty(pokemon)) {
            Debug.LogError($"Invertboost null {pokemon}");
            unit.TryEndExecute();
            return;
        }
        var battlerModel = mainActionExecutor.battleContext.GetPokeBy(pokemon);
        battlerModel.BoostInfo.Attack = -battlerModel.BoostInfo.Attack;
        battlerModel.BoostInfo.Defense = -battlerModel.BoostInfo.Defense;
        battlerModel.BoostInfo.SpecialAttack = -battlerModel.BoostInfo.SpecialAttack;
        battlerModel.BoostInfo.SpecialDefense = -battlerModel.BoostInfo.SpecialDefense;
        battlerModel.BoostInfo.Speed = -battlerModel.BoostInfo.Speed;
        battlerModel.BoostInfo.Accuracy = -battlerModel.BoostInfo.Accuracy;
        battlerModel.BoostInfo.Evasion = -battlerModel.BoostInfo.Evasion;
        unit.TryEndExecute();
    }

    public async Task Clearallboost(BattleMessageUnit unit)
    {   
        mainActionExecutor.ShowMessage("能力被还原了");
        var models = mainActionExecutor.battleContext.AllyBattlerModels(true).SelectMany(value => value.Value);
        foreach (var battlerModel in models)
        {
            battlerModel.ClearAllBoosts();
        }
        unit.TryEndExecute();
    }

    public async Task Crit(BattleMessageUnit unit)
    {
        string? pokemon = unit.NextValue();
        Debug.Log("Crit: " + pokemon);
        unit.TryEndExecute();
    }

    public async Task Supereffective(BattleMessageUnit unit)
    {
        string? pokemon = unit.NextValue();
        Debug.Log("Supereffective: " + pokemon);
        unit.TryEndExecute();
        // mainActionExecutor.ShowMessage($"{pokemon}效果绝佳");
    }

    public async Task Resisted(BattleMessageUnit unit)
    {
        string? pokemon = unit.NextValue();
        Debug.Log("Resisted: " + pokemon);
        unit.TryEndExecute();
    }

    public async Task Immune(BattleMessageUnit unit)
    {
        string? pokemon = unit.NextValue();
        pokemon = string.IsNullOrEmpty(unit.Of)? pokemon : unit.Of;
        if(!string.IsNullOrEmpty(pokemon)) {
            var battlerModel = mainActionExecutor.battleContext.GetPokeBy(pokemon);
            mainActionExecutor.ShowMessage($"{battlerModel.NameLocalized}免疫");
            battlerModel.ActivateAbility(unit.From);
        }
        unit.TryEndExecute();
    }

    public async Task Miss(BattleMessageUnit unit)
    {
        string? source = unit.NextValue();
        string? target = unit.NextValue();
        if(string.IsNullOrEmpty(source) || string.IsNullOrEmpty(target)) {
            Debug.LogError($"Miss null {source} {target}");
            unit.TryEndExecute();
            return;
        }
        var sourceModel = mainActionExecutor.battleContext.GetPokeBy(source);
        var targetModel = mainActionExecutor.battleContext.GetPokeBy(target);
        mainActionExecutor.ShowMessage($"{sourceModel.NameLocalized}技能被{targetModel.NameLocalized}躲过");
    }

    public async Task Fail(BattleMessageUnit unit)
    {
        string? pokemon = unit.NextValue();
        string? effect = unit.NextValue();
        mainActionExecutor.ShowMessage($"FailAction {pokemon} {effect}执行失败");
        unit.TryEndExecute();
        // throw new System.NotImplementedException();
    }

    public async Task Block(BattleMessageUnit unit)
    {
        throw new System.NotImplementedException();
    }

    public async Task Center(BattleMessageUnit unit)
    {
        unit.TryEndExecute();
    }

    public async Task Notarget(BattleMessageUnit unit)
    {
        unit.TryEndExecute();
    }

    public async Task Ohko(BattleMessageUnit unit)
    {
        unit.TryEndExecute();
    }

    public async Task Combine(BattleMessageUnit unit)
    {
        unit.TryEndExecute();
    }

    public async Task Hitcount(BattleMessageUnit unit)
    {
        unit.TryEndExecute();
    }

    public async Task Waiting(BattleMessageUnit unit)
    {
        unit.TryEndExecute();
    }

    public async Task Zbroken(BattleMessageUnit unit)
    {
        unit.TryEndExecute();
    }

    public async Task Zpower(BattleMessageUnit unit)
    {
        string? pokemon = unit.NextValue();
        if(string.IsNullOrEmpty(pokemon)) {
            Debug.LogError($"Zpower null {pokemon}");
            unit.TryEndExecute();
            return;
        }
        var battlerModel = mainActionExecutor.battleContext.GetPokeBy(pokemon);
        await mainActionExecutor.BattleController.Field.AnimateTransform(battlerModel, MainServer.BattleSpecialMove.SpecialMoveZmove);
        unit.TryEndExecute();
    }

    public async Task Prepare(BattleMessageUnit unit)
    {
        string? attacker = unit.NextValue();
        string? move = unit.NextValue();
        Debug.Log($"Prepare {attacker} {move}");
        unit.TryEndExecute();
    }

    public async Task Mustrecharge(BattleMessageUnit unit)
    {
        throw new System.NotImplementedException();
    }

    public async Task Status(BattleMessageUnit unit)
    {
        throw new System.NotImplementedException();
    }

    public async Task Curestatus(BattleMessageUnit unit)
    {
        throw new System.NotImplementedException();
    }

    public async Task Cureteam(BattleMessageUnit unit)
    {
        throw new System.NotImplementedException();
    }

    public async Task Item(BattleMessageUnit unit)
    {
        throw new System.NotImplementedException();
    }

    public async Task Enditem(BattleMessageUnit unit)
    {
        
    }

    public async Task Ability(BattleMessageUnit unit)
    {
        string? pokemon = unit.NextValue();
        string? ability = unit.NextValue();
        string? effect = unit.From;
        if(string.IsNullOrEmpty(pokemon) || string.IsNullOrEmpty(ability)) {
            Debug.LogError($"Ability null {pokemon} {ability}");
            unit.TryEndExecute();
            return;
        }
        NinBattleAbilityModel abilityModel = NinBattleAbilityModel.Create(ability);
        var poke = mainActionExecutor.battleContext.GetPokeBy(pokemon);
        poke.RememberAbility(ability, effect != null && ExecutingUnit.Fail == null);
        unit.TryEndExecute();
    }

    public async Task Endability(BattleMessageUnit unit)
    {
        string? pokemon = unit.NextValue();
        if(string.IsNullOrEmpty(pokemon)) {
            Debug.LogError($"Endability null {pokemon}");
            unit.TryEndExecute();
            return;
        }
        string? ability = unit.NextValue();
        var poke = mainActionExecutor.battleContext.GetPokeBy(pokemon);
        Debug.Log($"Endability {pokemon} {ability}");
        unit.TryEndExecute();
        // if(!string.IsNullOrEmpty(ability) && poke.BaseAbility) {
        //     NinBattleAbilityModel abilityModel = NinBattleAbilityModel.Create(ability);
        // }
        // throw new System.NotImplementedException();
    }

    public async Task Detailschange(BattleMessageUnit unit)
    {
        throw new System.NotImplementedException();
    }

    public async Task Transform(BattleMessageUnit unit)
    {
        string? pokemon = unit.NextValue();
        string? species = unit.NextValue();
        if(string.IsNullOrEmpty(pokemon) || string.IsNullOrEmpty(species)) {
            Debug.LogError($"Transform null {pokemon} {species}");
            unit.TryEndExecute();
            return;
        }
        //The Pokémon POKEMON has transformed into SPECIES by the move Transform or the ability Imposter.
        if(species == "Dynamax") { //普通的dynamax
            var poke = mainActionExecutor.battleContext.GetPokeBy(pokemon);
            if(poke.speciesForme != species) {
                // mainActionExecutor.battleContext.FormeChange(pokemon, species, null);
            }
            await mainActionExecutor.BattleController.Field.AnimateTransform(poke, MainServer.BattleSpecialMove.SpecialMoveMax);
            // mainActionExecutor.battleContext.GetPokeBy(pokemon).IsDynamax = true;
        } else if(species.EndsWith("-Gmax")) { //terastallize
            // mainActionExecutor.battleContext.GetPokeBy(pokemon).IsTerastallized = true;
        }
        throw new System.NotImplementedException();
    }

    public async Task Formechange(BattleMessageUnit unit)
    {
        throw new System.NotImplementedException();
    }

    public async Task Mega(BattleMessageUnit unit)
    {
        string? pokemon = unit.NextValue();
        if(string.IsNullOrEmpty(pokemon)) {
            Debug.LogError($"Mega null {pokemon}");
            unit.TryEndExecute();
            return;
        }
        var battlerModel = mainActionExecutor.battleContext.GetPokeBy(pokemon);
        await mainActionExecutor.BattleController.Field.AnimateTransform(battlerModel, MainServer.BattleSpecialMove.SpecialMoveMega);
        unit.TryEndExecute();
    }

    public async Task Primal(BattleMessageUnit unit)
    {
        string? pokemon = unit.NextValue();
        if(string.IsNullOrEmpty(pokemon)) {
            Debug.LogError($"Mega null {pokemon}");
            unit.TryEndExecute();
            return;
        }
        var battlerModel = mainActionExecutor.battleContext.GetPokeBy(pokemon);
        await mainActionExecutor.BattleController.Field.AnimateTransform(battlerModel, MainServer.BattleSpecialMove.SpecialMoveMega);
        unit.TryEndExecute();
    }

    public async Task Burst(BattleMessageUnit unit)
    {
        throw new System.NotImplementedException();
    }

    public async Task Terastallize(BattleMessageUnit unit)
    {
        string? pokemon = unit.NextValue();
        string? teraType = unit.NextValue();
        if(string.IsNullOrEmpty(pokemon) || string.IsNullOrEmpty(teraType)) {
            Debug.LogError($"Terastallize null {pokemon}");
            unit.TryEndExecute();
            return;
        }
        var battlerModel = mainActionExecutor.battleContext.GetPokeBy(pokemon) as PsBattleModel.BattlePoke;
        battlerModel.teraType = teraType;
        await mainActionExecutor.BattleController.Field.AnimateTransform(battlerModel, MainServer.BattleSpecialMove.SpecialMoveTerastallize);
        unit.TryEndExecute();
    }

    public async Task Start(BattleMessageUnit unit)
    {
        string? pokemon = unit.NextValue();
        string? effect = unit.NextValue();
        if(string.IsNullOrEmpty(pokemon) || string.IsNullOrEmpty(effect)) {
            Debug.LogError($"Start null {pokemon} {effect}");
            unit.TryEndExecute();
            return;
        }
        switch(effect.ToLower()) {
            case "typechange":
				break;
			case "typeadd":
				break;
			case "dynamax":
                var battlerModel = mainActionExecutor.battleContext.GetPokeBy(pokemon);
                await mainActionExecutor.BattleController.Field.AnimateTransform(battlerModel, MainServer.BattleSpecialMove.SpecialMoveMax);
				break;
			case "powertrick":
				break;
			case "foresight":
			case "miracleeye":
				break;
			case "telekinesis":
				break;
			case "confusion":
				break;
			case "leechseed":
				break;
			case "healblock":
				break;
			case "yawn":
				break;
			case "taunt":
				break;
			case "imprison":
				break;
			case "disable":
				break;
			case "embargo":
				break;
			case "torment":
				break;
			case "ingrain":
				break;
			case "aquaring":
				break;
			case "stockpile1":
				break;
			case "stockpile2":
				break;
			case "stockpile3":
				break;
			case "perish0":
				break;
			case "perish1":
				break;
			case "perish2":
				break;
			case "perish3":
				break;
			case "encore":
				break;
			case "bide":
				break;
			case "attract":
				break;
			case "autotomize":
				break;
			case "focusenergy":
				break;
			case "curse":
				break;
			case "nightmare":
				break;
			case "magnetrise":
				break;
			case "smackdown":
				break;
			case "substitute":
				break;

			// Gen 1-2
			case "mist":
				break;
			// Gen 1
			case "lightscreen":
				break;
			case "reflect":
				break;
        }
        unit.TryEndExecute();
    }

    public async Task End(BattleMessageUnit unit)
    {
        throw new System.NotImplementedException();
    }

    public async Task Singleturn(BattleMessageUnit unit)
    {
        throw new System.NotImplementedException();
    }

    public async Task Singlemove(BattleMessageUnit unit)
    {
        throw new System.NotImplementedException();
    }

    public async Task Activate(BattleMessageUnit unit)
    {
        throw new System.NotImplementedException();
    }

    public async Task Sidestart(BattleMessageUnit unit)
    {
        throw new System.NotImplementedException();
    }

    public async Task Sideend(BattleMessageUnit unit)
    {
        throw new System.NotImplementedException();
    }

    public async Task Swapsideconditions(BattleMessageUnit unit)
    {
        throw new System.NotImplementedException();
    }

    public async Task Weather(BattleMessageUnit unit)
    {
        string? weather = unit.NextValue();
        if(string.IsNullOrEmpty(weather)) {
            Debug.LogError($"Weather null {weather}");
            unit.TryEndExecute();
            return;
        }
        // if(upkeep) {
        //     // mainActionExecutor.BattleContext.weather.
        // }
        NinBattlerEffectState state = new(weather);
        if(state.IsWeather) {
            if(unit.From != null && unit.Of != null) {
                BattlePSEffect effect = new BattlePSEffect(ExecutingUnit.From);
                IBattlePoke ninBattlerModel = mainActionExecutor.battleContext.GetPokeBy(ExecutingUnit.Of);
                if(effect.IsAbility) {
                    ninBattlerModel.ActivateAbility(effect.Name);
                }
            }
            mainActionExecutor.battleContext.weather = state;
            mainActionExecutor.BattleController.Field.StartAnimateWeather(mainActionExecutor.battleContext.weather.Name, () => {
                mainActionExecutor.BattleController.battleUIMgr.battleUI.UpdateWeather().Forget();
                unit.TryEndExecute();
            });
        } else {
            unit.TryEndExecute();
        }
    }

    public async Task Fieldstart(BattleMessageUnit unit)
    {
        throw new System.NotImplementedException();
    }

    public async Task Fieldend(BattleMessageUnit unit)
    {
        throw new System.NotImplementedException();
    }

    public async Task Fieldactivate(BattleMessageUnit unit)
    {
        throw new System.NotImplementedException();
    }

    public async Task Anim(BattleMessageUnit unit)
    {
        throw new System.NotImplementedException();
    }

    public async Task Hint(BattleMessageUnit unit)
    {
        unit.TryEndExecute();
    }

    public async Task Message(BattleMessageUnit unit)
    {
        unit.TryEndExecute();
    }

    public async Task Candynamax(BattleMessageUnit unit)
    {
        unit.TryEndExecute();
    }
    

    // public async Task Ability(string pokemon, string ability, string effect)
    // {
    //     NinBattleAbilityModel abilityModel = NinBattleAbilityModel.Create(ability);
    //     var poke = mainActionExecutor.battleContext.GetPokeBy(pokemon);
    //     poke.RememberAbility(ability, effect != null && ExecutingUnit.Fail == null);
    //     if(ExecutingUnit.Silent != null) {

    //     } else if(effect != null){
    //         switch (effect) {
    // 			case "trace":
    //                 // poke.RememberAbility(ability, )
    //                 if(ExecutingUnit.Of != null) {
    //                     var ofpoke = mainActionExecutor.battleContext.GetPokeBy(ExecutingUnit.Of);
    //                     if(ofpoke != null) {
    //                         ofpoke.RememberAbility(ability);
    //                     }
    //                 }
    // 				break;
    // 			case "powerofalchemy":
    // 			case "receiver":
    //             if(ExecutingUnit.Of != null) {
    //                     var ofpoke = mainActionExecutor.battleContext.GetPokeBy(ExecutingUnit.Of);
    //                     if(ofpoke != null) {
    //                         ofpoke.RememberAbility(ability);
    //                     }
    //                 }
    // 				// this.activateAbility(poke, effect.name);
    // 				// this.scene.wait(500);
    // 				// this.activateAbility(poke, ability.name, true);
    // 				// ofpoke!.rememberAbility(ability.name);
    // 				break;
    // 			case "roleplay":
    // 				// this.activateAbility(poke, ability.name, true);
    // 				// ofpoke!.rememberAbility(ability.name);
    // 				break;
    // 			case "desolateland":
    // 			case "primordialsea":
    // 			case "deltastream":
    // 				// if (kwArgs.fail) {
    // 				// 	this.activateAbility(poke, ability.name);
    // 				// }
    // 				break;
    // 			default:
    // 				// this.activateAbility(poke, ability.name);
    // 				break;
    // 			}
    //     } else {

    //     }
    //     // ExecutingUnit.
    //     // let poke = this.getPokemon(args[1])!;
    //     // let ability = Dex.abilities.get(args[2]);
    //     // let effect = Dex.getEffect(kwArgs.from);
    //     // let ofpoke = this.getPokemon(kwArgs.of);
    //     // poke.rememberAbility(ability.name, effect.id && !kwArgs.fail);

    //     throw new System.NotImplementedException();
    // }

    // public async Task AbilityRevealed(string pokemon, string ability)
    // {
    //     throw new System.NotImplementedException();
    // }

    // public async Task AbilitySuppressed(string pokemon)
    // {
    //     throw new System.NotImplementedException();
    // }

    // public async Task ActivateEffect(string effect)
    // {
    //     mainActionExecutor.ShowMessage($"激活了{effect}");
    //     ExecutingUnit.TryEndExecute();
    // }

    // public async Task ActiveFieldCondition(string condition)
    // {
    //     throw new System.NotImplementedException();
    // }

    // public async Task ApplyVolatileStatus(string pokemon, string effect, string? from, string? of, string ext)
    // {
    //     // if(effect == "Disable" && ext != null) {
    //     //    var poke = mainActionExecutor.BattleContext.GetPokeBy(pokemon);
    //     //    if(poke != null) {
    //     //         poke.DisableMoves(new string[] {ext});
    //     //    }
    //     // }
    //     //|-start|p1a: Falinks|Disable|Throat Chop|[from] ability: Cursed Body|[of] p2a: Gengar 
    //     mainActionExecutor.ShowMessage($"开始{effect} {from} {of}");
    //     ExecutingUnit.TryEndExecute();
    // }

    // public async Task BlockAction(string pokemon, string effect, string move, string attacker)
    // {
    //     throw new System.NotImplementedException();
    // }


    // public async Task CenterPokemon()
    // {
    //     throw new System.NotImplementedException();
    // }

    // public async Task ChangeWeather(string weather, bool upkeep)
    // {
    //     if(upkeep) {
    //         // mainActionExecutor.BattleContext.weather.
    //     }
    //     NinBattlerEffectState state = new(weather);
    //     if(state.IsWeather) {
    //         if(ExecutingUnit.From != null && ExecutingUnit.Of != null) {
    //             BattlePSEffect effect = new BattlePSEffect(ExecutingUnit.From);
    //             IBattlePoke ninBattlerModel = mainActionExecutor.battleContext.GetPokeBy(ExecutingUnit.Of);
    //             if(effect.IsAbility) {
    //                 ninBattlerModel.ActivateAbility(effect.Name);
    //             }
    //         }
    //         mainActionExecutor.battleContext.weather = state;
    //         mainActionExecutor.BattleController.Field.StartAnimateWeather(mainActionExecutor.battleContext.weather.Name, () => {
    //             mainActionExecutor.BattleController.battleUIMgr.battleUI.UpdateWeather().Forget();
    //             ExecutingUnit.TryEndExecute();
    //         });
    //     } else {
    //         ExecutingUnit.TryEndExecute();
    //     }
    // }
    // // changeWeather(weatherName: string, poke?: Pokemon, isUpkeep?: boolean, ability?: Effect) {
    // // 	let weather = toID(weatherName);
    // // 	if (!weather || weather === "none") {
    // // 		weather = "" as ID;
    // // 	}
    // // 	if (isUpkeep) {
    // // 		if (this.weather && this.weatherTimeLeft) {
    // // 			this.weatherTimeLeft--;
    // // 			if (this.weatherMinTimeLeft !== 0) this.weatherMinTimeLeft--;
    // // 		}
    // // 		if (this.seeking === null) {
    // // 			this.scene.upkeepWeather();
    // // 		}
    // // 		return;
    // // 	}
    // // 	if (weather) {
    // // 		let isExtremeWeather = (weather === "deltastream" || weather === "desolateland" || weather === "primordialsea");
    // // 		if (poke) {
    // // 			if (ability) {
    // // 				this.activateAbility(poke, ability.name);
    // // 			}
    // // 			this.weatherTimeLeft = (this.gen <= 5 || isExtremeWeather) ? 0 : 8;
    // // 			this.weatherMinTimeLeft = (this.gen <= 5 || isExtremeWeather) ? 0 : 5;
    // // 		} else if (isExtremeWeather) {
    // // 			this.weatherTimeLeft = 0;
    // // 			this.weatherMinTimeLeft = 0;
    // // 		} else {
    // // 			this.weatherTimeLeft = (this.gen <= 3 ? 5 : 8);
    // // 			this.weatherMinTimeLeft = (this.gen <= 3 ? 0 : 5);
    // // 		}
    // // 	}
    // // 	this.weather = weather;
    // // 	this.scene.updateWeather();
    // // }

    // public async Task ClearAllBoosts()
    // {
    //     var models = mainActionExecutor.battleContext.AllyBattlerModels(true).SelectMany(value => value.Value);
    //     foreach (var battlerModel in models)
    //     {
    //         battlerModel.ClearAllBoosts();
    //     }
    //     ExecutingUnit.TryEndExecute();
    // }

    // public async Task ClearBoost(string pokemon)
    // {
    //     var battlerModel = mainActionExecutor.battleContext.GetPokeBy(pokemon);
    //     battlerModel.ClearAllBoosts();
    //     ExecutingUnit.TryEndExecute();
    // }

    // public async Task ClearNegativeBoost(string pokemon)
    // {
    //     var battlerModel = mainActionExecutor.battleContext.GetPokeBy(pokemon);
    //     if(battlerModel.BoostInfo.Attack < 0) {
    //         battlerModel.BoostInfo.Attack = 0;
    //     }
    //     if(battlerModel.BoostInfo.Defense < 0) {
    //         battlerModel.BoostInfo.Defense = 0;
    //     }
    //     if(battlerModel.BoostInfo.SpecialAttack < 0) {
    //         battlerModel.BoostInfo.SpecialAttack = 0;
    //     }
    //     if(battlerModel.BoostInfo.SpecialDefense < 0) {
    //         battlerModel.BoostInfo.SpecialDefense = 0;
    //     }
    //     if(battlerModel.BoostInfo.Speed < 0) {
    //         battlerModel.BoostInfo.Speed = 0;
    //     }
    //     if(battlerModel.BoostInfo.Accuracy < 0) {
    //         battlerModel.BoostInfo.Accuracy = 0;
    //     }
    //     if(battlerModel.BoostInfo.Evasion < 0) {
    //         battlerModel.BoostInfo.Evasion = 0;
    //     }
    //     // var localPokemonData = battlerModel.LocalPokemonData();
    //     mainActionExecutor.ShowMessage($"{battlerModel.NameLocalized} 能力恢复了");
    //     // throw new System.NotImplementedException();
    // }

    // public async Task ClearPositiveBoost(string target, string pokemon, string effect)
    // {
    //     var battlerModel = mainActionExecutor.battleContext.GetPokeBy(pokemon);
    //     if(battlerModel.BoostInfo.Attack > 0) {
    //         battlerModel.BoostInfo.Attack = 0;
    //     }
    //     if(battlerModel.BoostInfo.Defense > 0) {
    //         battlerModel.BoostInfo.Defense = 0;
    //     }
    //     if(battlerModel.BoostInfo.SpecialAttack > 0) {
    //         battlerModel.BoostInfo.SpecialAttack = 0;
    //     }
    //     if(battlerModel.BoostInfo.SpecialDefense > 0) {
    //         battlerModel.BoostInfo.SpecialDefense = 0;
    //     }
    //     if(battlerModel.BoostInfo.Speed > 0) {
    //         battlerModel.BoostInfo.Speed = 0;
    //     }
    //     if(battlerModel.BoostInfo.Accuracy > 0) {
    //         battlerModel.BoostInfo.Accuracy = 0;
    //     }
    //     if(battlerModel.BoostInfo.Evasion > 0) {
    //         battlerModel.BoostInfo.Evasion = 0;
    //     }   
    //     // var localPokemonData = await battlerModel.LocalPokemonData();
    //     mainActionExecutor.ShowMessage($"{battlerModel.NameLocalized} 能力被还原了");
    //     // throw new System.NotImplementedException();
    // }

    // public async Task CombineMove()
    // {
    //     throw new System.NotImplementedException();
    // }

    // public async Task CopyBoost(string source, string target)
    // {
    //     throw new System.NotImplementedException();
    // }

    // public async Task CriticalHit(string pokemon)
    // {
    //     throw new System.NotImplementedException();
    // }

    // public async Task CureStatus(string pokemon, string status)
    // {
    //     throw new System.NotImplementedException();
    // }

    // public async Task CureTeam(string pokemon)
    // {
    //     throw new System.NotImplementedException();
    // }


    // public async Task EndFieldCondition(string condition)
    // {
    //     throw new System.NotImplementedException();
    // }

    // public async Task EndSideCondition(string side, string condition)
    // {
    //     throw new System.NotImplementedException();
    // }

    // public async Task FailAction(string pokemon, string action)
    // {
    //     // throw new System.NotImplementedException();
    //     mainActionExecutor.ShowMessage($"FailAction {pokemon} {action}执行失败");
    //     ExecutingUnit.TryEndExecute();
    // }


    // public async Task InflictStatus(string pokemon, string status)
    // {
    //     throw new System.NotImplementedException();
    // }

    // public async Task InvertBoost(string pokemon)
    // {
    //     throw new System.NotImplementedException();
    // }

    // public async Task ItemChanged(string pokemon, string item, string? effect)
    // {
    //     mainActionExecutor.BattleController.Field.Item(pokemon, item, effect);
    //     ExecutingUnit.TryEndExecute();
    //     // throw new System.NotImplementedException();
    // }

    // public async Task ItemConsumed(string pokemon, string item)
    // {
    //     throw new System.NotImplementedException();
    // }

    // public async Task ItemDestroyed(string pokemon, string item, string effect)
    // {
    //     throw new System.NotImplementedException();
    // }

    // public async Task ItemRevealed(string pokemon, string item)
    // {
    //     throw new System.NotImplementedException();
    // }

    // public async Task MegaEvolve(string pokemon, string megaStone)
    // {
    //     throw new System.NotImplementedException();
    // }

    // public async Task MoveDidNothing()
    // {
    //     mainActionExecutor.ShowMessage($"技能什么都没有发生");
    //     ExecutingUnit.TryEndExecute();
    // }

    // public async Task MoveImmune(string pokemon)
    // {
    //     mainActionExecutor.ShowMessage($"{pokemon}免疫");
    //     ExecutingUnit.TryEndExecute();
    // }

    // public async Task MoveMissed(string source, string target)
    // {
    //     mainActionExecutor.ShowMessage($"{source}技能被{target}躲过");
    //     ExecutingUnit.TryEndExecute();
    // }

    // public async Task MoveResisted(string pokemon)
    // {
    //     throw new System.NotImplementedException();
    // }

    // public async Task MultiHitMove(string pokemon, int hitCount)
    // {
    //     throw new System.NotImplementedException();
    // }

    // public async Task MustRecharge(string pokemon)
    // {
    //     throw new System.NotImplementedException();
    // }

    // public async Task NoTarget(string pokemon)
    // {
    //     throw new System.NotImplementedException();
    // }

    // public async Task PrepareMove(string attacker, string move)
    // {
    //     throw new System.NotImplementedException();
    // }

    // public async Task PrepareMove(string attacker, string move, string defender)
    // {
    //     throw new System.NotImplementedException();
    // }

    // public async Task PrimalRevert(string pokemon)
    // {
    //     // throw new System.NotImplementedException();
    //     mainActionExecutor.ShowMessage($"PrimalRevert {pokemon} 回归原始形态");
    //     var poke = mainActionExecutor.battleContext.GetPokeBy(pokemon);
    //     //poke在会在上一个unit中就改变了，（detailschange）
    //     await mainActionExecutor.BattleController.Field.AnimateTransform(poke, MainServer.BattleSpecialMove.SpecialMoveMega);
    //     ExecutingUnit.TryEndExecute();
    // }

    // public async Task RemoveVolatileStatus(string pokemon, string effect)
    // {
    //     throw new System.NotImplementedException();
    // }

    // public async Task SetBoost(string pokemon, string stat, int amount)
    // {
    //     throw new System.NotImplementedException();
    // }


    // public async Task ShowCustomMessage(string message)
    // {
    //     throw new System.NotImplementedException();
    // }

    // public async Task ShowHint(string message)
    // {
    //     throw new System.NotImplementedException();
    // }

    // public async Task SingleMoveEffect(string pokemon, string move)
    // {
    //     throw new System.NotImplementedException();
    // }

    // public async Task SingleTurnEffect(string pokemon, string move)
    // {
    //     var poke = mainActionExecutor.battleContext.GetPokeBy(pokemon);
    //     poke.PokeStatus.AddTurnstatus(move);
    //     mainActionExecutor.BattleController.Field.ResultAni(poke, ()=>{
    //         ExecutingUnit.TryEndExecute();
    //     });
    //     // throw new System.NotImplementedException();
    // }

    // public async Task StartFieldCondition(string condition)
    // {
    //     throw new System.NotImplementedException();
    // }

    // public async Task StartSideCondition(string side, string condition)
    // {
    //     throw new System.NotImplementedException();
    // }

    // public async Task SuperEffective(string pokemon)
    // {
    //     mainActionExecutor.ShowMessage($"{pokemon}效果绝佳");
    //     throw new System.NotImplementedException();
    // }

    // public async Task SwapBoost(string source, string target, string stats)
    // {
    //     throw new System.NotImplementedException();
    // }

    // public async Task SwapSideConditions()
    // {
    //     throw new System.NotImplementedException();
    // }

    // public async Task Transform(string pokemon, string species)
    // {
    //     //The Pokémon POKEMON has transformed into SPECIES by the move Transform or the ability Imposter.
    //     if(species == "Dynamax") { //普通的dynamax
    //         var poke = mainActionExecutor.battleContext.GetPokeBy(pokemon);
    //         if(poke.speciesForme != species) {
    //             // mainActionExecutor.battleContext.FormeChange(pokemon, species, null);
    //         }
    //         await mainActionExecutor.BattleController.Field.AnimateTransform(poke, MainServer.BattleSpecialMove.SpecialMoveMax);
    //         // mainActionExecutor.battleContext.GetPokeBy(pokemon).IsDynamax = true;
    //     } else if(species.EndsWith("-Gmax")) { //terastallize
    //         // mainActionExecutor.battleContext.GetPokeBy(pokemon).IsTerastallized = true;
    //     }
    //     throw new System.NotImplementedException();
    // }

    // public async Task UltraBurst(string pokemon, string species, string item)
    // {
    //     throw new System.NotImplementedException();
    // }


    // public async Task UseZPower(string pokemon)
    // {
    //     // throw new System.NotImplementedException();
    //     mainActionExecutor.ShowMessage($"{pokemon} 使用ZPower");
    //     var poke = mainActionExecutor.battleContext.GetPokeBy(pokemon);
    //     await mainActionExecutor.BattleController.Field.AnimateTransform(poke, MainServer.BattleSpecialMove.SpecialMoveZmove);
    //     ExecutingUnit.TryEndExecute();
    // }

    // public async Task WaitingForTarget(string source, string target)
    // {
    //     throw new System.NotImplementedException();
    // }

    // public async Task ZMoveBreakThrough(string pokemon)
    // {
    //     throw new System.NotImplementedException();
    // }

    // public override bool Execute(BattleMessageUnit unit)
    // {
    //     var result = base.Execute(unit);
    //     TryEndExecuteUnit(unit);
    //     return result;
    // }
}
