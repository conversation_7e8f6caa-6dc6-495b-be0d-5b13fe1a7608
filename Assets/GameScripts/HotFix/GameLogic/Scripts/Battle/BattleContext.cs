using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using PsBattleModel;
using Nakama.TinyJson;
using PetsServices;
using PetsServices.Battle;
using PetsServices.Net;
using UnityEngine;
using Cysharp.Threading.Tasks;
public enum BattleTargetType
{
    Wild, //野生
    NPC,
    Player
}
public class BattleContext
{
    public BattleTargetType TargetType = BattleTargetType.Wild;
    public bool replay = false;
    public string BattleId; //可以是匹配id 或者其他
    public int Turn = 0;
    //singles, doubles, triples, multi, or freeforall.
    public string GameType;
    public int Gen;
    public string Tier;
    public NinBattlerEffectState? weather;
    public NinBattlerEffectState[] pseudoWeathers = new NinBattlerEffectState[0];
    // public NinBattlerEffectState[] sideConditions;
    // public SideInfo Side;//自己的信息
    // public BattlePokePosition Position;//观看的视角
    public int playerSiden
    {
        get
        {
            return _playerSiden;
        }
        set
        {
            _playerSiden = value; //调整视角
        }
    }
    public bool CheckTeamAckAndAdd(string? player = null)
    {
        if (player != null)
        {
            var PokeIdInfo = BattlePokeIdInfo.ParsePokeIdInfo(player);
            if (PlayerMap.TryGetValue(PokeIdInfo.siden, out BattlePlayer value))
            {
                value.teamAck = true;
            }

        }
        foreach (var kvp in PlayerMap)
        {
            if (kvp.Value.trainer.Id > 0 && !kvp.Value.teamAck)
            {
                return false;
            }
        }
        return true;
    }
    private int _playerSiden = 1;//默认视角 //默认视角
    // private string _playerSideId = "p1"; 
    public void SetPlayerSideId(string sideId)
    {
        playerSiden = BattlePokeIdInfo.ParsePokeIdInfo(sideId).siden;
    }
    public long StartTime = 0;
    // public readonly BattleMessageHandler messageHandler;
    public BattleActionExecutor actionExecutor;
    public Dictionary<int, BattlePlayer> PlayerMap = new(); //playerIndex to player
    MainServer.Trainer[] _trainers;
    // public BattlePlayer myBattlePlayer;
    // public Dictionary<int, List<NinBattlerModel>> PlayerPokeMap = new();
    // public Dictionary<int, int> PlayerTeamSizeMap = new();
    // public Dictionary<string, SideInfo> sideInfos;
    private BattleRequest _lastBattleRequestInfo;
    public BattleRequest lastBattleRequestInfo {
        get {
            return _lastBattleRequestInfo;
        }
        set {
            _lastBattleRequestInfo = value;
            // foreach (var pokeInfo in _lastBattleRequestInfo.Side.Pokemon)
            // {
            //     var pokeModel = GetPokeBy(poke.Ident);
            //     pokeModel.UpdateInfo(pokeInfo)
            // }
            // _lastBattleRequestInfo.Side.
        }
    }
    private BattleDecision _battleDecision; //收到Request进行重置
    public BattleDecision BattleDecision
    {
        get
        {
            return _battleDecision;
        }
    }

    // public bool MyIsObb
    // {
    //     get
    //     {
    //         return true;
    //     }
    // }
    //一个人能选多少人出战
    public int BattleTeamCount
    {
        get
        {
            return 6;
        }
    }
    public int PlayerNumber
    { //
        get
        {
            return PlayerMap.Keys.Count;
        }
    }
    public int SideBattlerNumber
    { //一边战斗的精灵数
        get
        {
            if (GameType == "singles")
            {
                return 1;
            }
            else if (GameType == "triples")
            {
                return 3;
            }
            return 2;
        }
    }
    public bool IsMyAlly(IBattlePoke model)
    {
        if (model.PokeIdInfo != null)
        {
            if ((model.PokeIdInfo.siden + playerSiden) % 2 == 0)
            {
                return true;
            }
        }
        return false;
    }
    public BattleContext(string bid, BattleActionExecutor actionExecutor)
    {
        BattleId = bid;
        this.actionExecutor = actionExecutor;
    }
    public void Clear()
    {

    }
    // public void SetFirstRequest(BattleRequest firstRequest) {
    //     //TODO 还要进行id的判断，判断是否是自己的，这样才设置side
    //     // Side = firstRequest.Side;
    //     Position = BattleModel.BattlePokePosition.ParsePokeIdInfo(firstRequest.Side.Id);
    // }
    // public BattleContext(BattleRequest firstRequest)
    // {
    //     //TODO 还要进行id的判断，判断是否是自己的，这样才设置side
    //     Side = firstRequest.Side;
    //     Position = BattleModel.BattlePokePosition.ParsePokeIdInfo(Side.Id);
    // }
    public void UpdateTurnCounters()
    {

    }
    public int GetPokeBoost(string pokemon, string statkey)
    {
        var poke = GetPokeBy(pokemon, null, null);
        var stat = ConstInfoTold.GetStat(statkey);
        if (poke == null || stat == null)
        {
            PLog.Error($"SetBoost {pokemon} {statkey}, {poke}, {stat}");
            return 0;
        }
        switch (stat)
        {
            case PokemonStatKey.Attack:
                return poke.BoostInfo.Attack;
            case PokemonStatKey.Defense:
                return poke.BoostInfo.Defense;
            case PokemonStatKey.SpecialAttack:
                return poke.BoostInfo.SpecialAttack;
            case PokemonStatKey.SpecialDefense:
                return poke.BoostInfo.SpecialDefense;
            case PokemonStatKey.Accuracy:
                return poke.BoostInfo.Accuracy;
            case PokemonStatKey.Evasion:
                return poke.BoostInfo.Evasion;
            default:
                return 0;
        }
    }
    public void SetBoost(string pokemon, string statkey, int amount)
    {
        var poke = GetPokeBy(pokemon, null, null);
        var stat = ConstInfoTold.GetStat(statkey);
        if (poke == null || stat == null)
        {
            PLog.Error($"SetBoost {pokemon} {statkey}, {poke}, {stat}");
            return;
        }
        if (amount > 6)
        {
            amount = 6;
        }
        if (amount < -6)
        {
            amount = -6;
        }
        switch (stat)
        {
            case PokemonStatKey.Attack:
                poke.BoostInfo.Attack = amount;
                break;
            case PokemonStatKey.Defense:
                poke.BoostInfo.Defense = amount;
                break;
            case PokemonStatKey.SpecialAttack:
                poke.BoostInfo.SpecialAttack = amount;
                break;
            case PokemonStatKey.SpecialDefense:
                poke.BoostInfo.SpecialDefense = amount;
                break;
            case PokemonStatKey.Accuracy:
                poke.BoostInfo.Accuracy = amount;
                break;
            case PokemonStatKey.Evasion:
                poke.BoostInfo.Evasion = amount;
                break;
        }
    }
    public void PokemonFainted(IBattlePoke pokeModel)
    {
        var poke = GetPokeBy(pokeModel.PokeIdInfo, null);
        if (poke != null)
        {
            poke.Faint();
            BringStackBottom(poke);
        }
    }
    public void Upkeep()
    {
        PLog.Info("upkeep");
        if (lastBattleRequestInfo != null)
        {
            SetBattleRequestInfo(lastBattleRequestInfo);
        }
        //更新回合
        foreach (var pseudoWeather in this.pseudoWeathers)
        {
            pseudoWeather.Turns++;
        }
        foreach (var pokes in this.AllBattlerModels())
        {
            foreach (var poke in pokes.Value)
            {
                poke.UpKeep();
            }
        }
    }
    // public void UpdateActiveInfo(PsBattleModel.ActiveInfo info)
    // {
    //     activeInfo = info;
    //     List<NinBattleMoveModel> models = new();
    //     foreach (var move in activeInfo.Moves)
    //     {
    //         models.Add(new NinBattleMoveModel(this, move));
    //     }
    //     battleMoveModels = models.ToArray();
    //     // moves = PokeDataLoad.Share.GetIndexDatas<Move>(pokemonInfo.Moves).Result.ToArray();
    // }
    public List<NinBattleMoveModel> GetNinBattleMoveModelsBy(IBattlePoke poke)
    {
        if(lastBattleRequestInfo == null || poke.slot == null) {
            return new();
        }
        if (!poke.IsActive || poke.slot > lastBattleRequestInfo.Active.Count)
        {
            PLog.Error($"poke.IsActive: {poke.IsActive}, poke slot: {poke.slot}, active poke count: {lastBattleRequestInfo.Active.Count}");
            return new();
        }
        List<NinBattleMoveModel> models = new();
        foreach (var move in lastBattleRequestInfo.Active[(int)poke.slot].Moves)
        {
            models.Add(new NinBattleMoveModel(poke, move));
        }
        return models;
    }
    void SetBattleRequestInfo(BattleRequest battleRequest)
    {
        // Position = BattleModel.BattlePokePosition.ParsePokeIdInfo(battleRequest.Side.Id);
        SetPlayerSideId(battleRequest.Side.Id);
        lastBattleRequestInfo = battleRequest;
        _battleDecision = new();
        // //倒序因为要同步active的顺序
        // for (int i = LastBattleRequestInfo.Side.Pokemon.Count - 1; i >= 0; i--)
        for (int i = 0; i < lastBattleRequestInfo.Side.Pokemon.Count; i++)
        {
            var pokemonInfo = lastBattleRequestInfo.Side.Pokemon[i];
            var pokeIdInfo = PsBattleModel.BattlePokeIdInfo.ParsePokeIdInfo(pokemonInfo.Ident);
            PsBattleModel.BattlePoke poke = new BattlePoke(pokemonInfo);
            var battler = GetPokeBy(pokeIdInfo, poke);
            if (battler != null)
            {
                battler.UpdateInfo(pokemonInfo);
            }
            else
            {
                AddPoke(poke);
                battler = poke;
                // 创建新模型
                // battler = new NinBattlerModel(pokemonInfo);
                // if (battler != null)
                // {
                //     AddPoke(battler);
                // }
            }
            var battlePoke = battler as BattlePoke;
            if(lastBattleRequestInfo.Active.Count > i && battlePoke != null) {
                var active = lastBattleRequestInfo.Active[i];
                battlePoke.UpdateMoves(active.Moves.ToArray());
            }
            // if (battleRequest.Active != null && i < battleRequest.Active.Count)
            // {
            //     var active = battleRequest.Active[i];
            //     battler.UpdateActiveInfo(active);
            // }
            //同步Request的顺序
            BringStackToIndex(battler, i);
        }
    }
    public void BringStackToIndex(IBattlePoke model, int index)
    {
        if (PlayerMap.TryGetValue(model.PokeIdInfo.siden, out var playerModel))
        {
            var pokes = playerModel.Pokes;
            if (pokes.Count < index)
            {
                throw new Exception("BringStackToIndex pokes.Count < index");
            }
            for (int i = 1; i < pokes.Count; i++)
            {
                if (pokes[i] == model)
                {
                    var temp = pokes[index];
                    pokes[index] = pokes[i];
                    pokes[i] = temp;
                    return;
                }
            }
        }
    }
    public void BringStackBottom(IBattlePoke model)
    {
        if (PlayerMap.TryGetValue(model.PokeIdInfo.siden, out var playerModel))
        {
            var pokes = playerModel.Pokes;
            for (int i = pokes.Count - 2; i > 0; i--) ///已经是最后一个不需要交换
            {
                if (pokes[i] == model)
                {
                    var temp = pokes[pokes.Count - 1];
                    pokes[pokes.Count - 1] = pokes[i];
                    pokes[i] = temp;
                    return;
                }
            }
        }
    }
    public void AddTrainer(string side, string username)
    {
        // GameContext.Current.Trainer
    }
    public BattlePlayer[] GetBattlePlayers()
    {
        BattlePlayer player = null;
        List<BattlePlayer> allyPlayers = new();
        List<BattlePlayer> otherPlayers = new();
        foreach (var item in PlayerMap)
        {
            if (playerSiden % 2 == item.Key % 2)
            {
                if (item.Key == playerSiden)
                {
                    player = item.Value;
                }
                else
                {
                    allyPlayers.Add(item.Value);
                }
            }
            else
            {
                otherPlayers.Add(item.Value);
            }
        }
        allyPlayers.OrderBy(player => player.PlayerSiden);
        otherPlayers.OrderBy(player => player.PlayerSiden);
        if (player != null)
        {
            allyPlayers.Insert(0, player);
        }
        return allyPlayers.Concat(otherPlayers).ToArray();
    }
    // AddPokemonToPreview
    public async UniTask<IBattlePoke> AddPokemonToPreview(string player, string details, bool hasItem)
    {
        //teamview上添加的，此时还没办法具体区分精灵，只有name
        var pokeIdInfo = BattlePokeIdInfo.ParsePokeIdInfo(player);
        var poke = new BattlePoke(player);
        poke.UpdateInfo(player, details, null);

        var battler = GetPokeBy(pokeIdInfo, poke);
        if (battler != null)
        {
            return battler;
        }
        else
        {
            // 创建新模型
            AddPoke(poke);
            // if(poke != null) {
            //     AddPoke(poke);
            // } else {
            //     poke = new BattlePoke(pokemonInfo);
            //     AddPoke(poke);
            // }
            // BattlePoke ninBattlerModel = new(player, details);
            // ninBattlerModel.battlePoke.hasItem = hasItem;
            // if (ninBattlerModel != null)
            // {
            //     AddPoke(ninBattlerModel);
            // }
            return poke;
        }
    }
    private void ClearPokeBy(int playerIndex)
    {
        PlayerMap.Remove(playerIndex);
        // PlayerMap[playerIndex] = new();
    }
    public void InitTrainers(MainServer.BattlePrepareOutputTrainerInfo[] infos)
    {
        foreach (var info in infos)
        {
            var player = new BattlePlayer();
            player.SetPlayer(info.PsPlayerKey);
            player.trainer = info.Trainer;
            PlayerMap[player.PlayerSiden] = player;
        }
    }
    private void AddPoke(IBattlePoke model)
    {
        List<IBattlePoke> pokes = new();
        var playerIndex = model.PokeIdInfo.siden;
        if (playerIndex < 0)
        {
            PLog.Error("AddPoke playerIndex < 0");
            return;
        }
        if (PlayerMap.TryGetValue(playerIndex, out var playerModel))
        {
            pokes = playerModel.Pokes;
        }
        pokes.Add(model);
        PlayerMap[playerIndex].Pokes = pokes;
    }
    public void SetTeamsize(string player, int size)
    {
        var position = PsBattleModel.BattlePokeIdInfo.ParsePokeIdInfo(player);
        PlayerMap[position.siden].TeamSize = size;
        //  List<NinBattlerModel> pokes = new();
        // if (PlayerMap.TryGetValue(position.playerIndex, out var value))
        // {
        //     pokes = value;
        // }
        // if(pokes.Count < size) {
        //     pokes.AddRange(new NinBattlerModel[size - pokes.Count]);
        // }
    }
    //优化
    // private NinBattlerModel HandleBattlePokeUpdate(
    // BattleModel.BattlePokePosition position,
    // BattleModel.BattlePoke? poke,
    // Action<NinBattlerModel> updateModelAction,
    // Func<NinBattlerModel?> createNewModelAction)
    // {
    //     var samePoke = GetPokeBy(position, poke);
    //     if(samePoke != null) {
    //         updateModelAction(samePoke);
    //         return samePoke;
    //     }
    //     // 创建新模型
    //     var newModel = createNewModelAction();
    //     if(newModel != null) {
    //         AddPoke(newModel);
    //     }
    //     return newModel;
    // }
    //poke用于模糊查找 因为存储的poke可能没有某些数据导致精确查找查找不到
    public IBattlePoke? GetPokeBy(BattlePokeIdInfo pokeIdInfo, IBattlePoke? poke)
    {
        IBattlePoke sameNamePoke = null;
        IBattlePoke sameIdPoke = null;
        if (PlayerMap.TryGetValue(pokeIdInfo.siden, out var player))
        {
            foreach (var item in player.Pokes)
            {
                if (pokeIdInfo.IsParticular() && pokeIdInfo.IsEqual(item.PokeIdInfo))
                {
                    return item; //精确查找
                    // sameIdPoke = item;
                    // break;
                }
                if (poke != null)
                {
                    if (poke.IsNearEqual(item) && sameNamePoke == null)
                    {
                        sameNamePoke = item;
                        continue;
                    }
                    // if (IsSameNamePoke(item, poke, pokeIdInfo, ref sameNamePoke))
                    // {
                    //     continue;
                    // }
                }
                // if (IsSameIdPoke(item, pokeIdInfo, ref sameIdPoke)) break;
            }

            // 更新已有模型
            if (sameIdPoke != null)
            {
                return sameIdPoke;
            }

            if (sameNamePoke != null)
            {
                return sameNamePoke;
            }
        }
        return null;
    }

    /// <summary>
    /// 检查是否是同名 Poke，更新引用并返回是否匹配成功。
    /// </summary>
    // private bool IsSameNamePoke(BattlePoke poke1, PsBattleModel.BattlePoke poke2)
    // {
    //     poke1.IsNearEqual(poke2)
    //     if ((poke1.PokeIdInfo == null || poke1.PokeIdInfo.pokeId == null || position.pokeId == null) &&
    //         poke1.battlePoke.IsNearEqual(poke2))
    //     {
    //         sameNamePoke = poke1;
    //         return true;
    //     }
    //     return false;
    // }

    /// <summary>
    /// 检查是否是同 ID Poke，更新引用并返回是否匹配成功。
    /// </summary>
    // private bool IsSameIdPoke(BattlePoke item, PsBattleModel.BattlePokeIdInfo position, ref BattlePoke sameIdPoke)
    // {
    //     if (item.PokeIdInfo != null && item.PokeIdInfo.pokeId != null && position.pokeId != null && item.PokeIdInfo.pokeId == position.pokeId)
    //     {
    //         sameIdPoke = item;
    //         return true;
    //     }
    //     return false;
    // }
    // public NinBattlerModel UpdateBattlePoke(PokemonInfo info)
    // {
    //     var position = BattleModel.BattlePokePosition.ParsePokeIdInfo(info.Ident);

    //     var poke = BattleModel.BattlePoke.ParseBattlePokeModel(info.Details, info.Condition);

    //     return HandleBattlePokeUpdate(
    //         position,
    //         poke,
    //         model => model.SetInfo(info), // 更新已有模型
    //         () => new NinBattlerModel(info) // 创建新模型
    //     );
    // }
    public IBattlePoke ChangeDetails(string pokemon, string details, string? hpStatus)
    {
        return CreateOrUpdateBattlePoke(pokemon, details, hpStatus);
    }
    public IBattlePoke FormeChange(string pokemon, string species, string hpStatus)
    {
        throw new Exception("FormeChange");
        // return CreateOrUpdateBattlePoke(pokemon, species, hpStatus);
    }
    public IBattlePoke UpdateBattlePokeHpStatus(string pokemon, string hpStatus)
    {
        return CreateOrUpdateBattlePoke(pokemon, null, hpStatus);
    }
    private IBattlePoke CreateOrUpdateBattlePoke(string pokemon, string? details, string? hpStatus)
    {
        var pokeIdInfo = BattlePokeIdInfo.ParsePokeIdInfo(pokemon);
        BattlePoke poke = new BattlePoke(pokemon);
        poke.UpdateInfo(pokemon, details, hpStatus);

        var battler = GetPokeBy(pokeIdInfo, poke);
        if (battler != null)
        {
            battler.UpdateInfo(pokemon, details, hpStatus);
            return battler;
        }
        else
        {
            if (details == null)
            {
                throw new Exception("CreateOrUpdateNonselfBattlePoke details = null");
            }
            AddPoke(poke);
            // 创建新模型
            // var newModel = new NinBattlerModel(pokemon, details, hpStatus);
            // if (newModel != null)
            // {
            //     AddPoke(newModel);
            // }
            return poke;
        }

    }

    public IBattlePoke SwitchBattlePoke(string pokemon, string details, string hpStatus)
    {
        // var pokeIdInfo = BattlePokeIdInfo.ParsePokeIdInfo(pokemon);
        // GetPokeBy
        // var battler = GetPokeBy(pokeIdInfo, poke);
        // PsBattleModel.BattlePoke poke = null;
        // if (!pokeIdInfo.IsParticular())
        // {
        //     poke = new PsBattleModel.BattlePoke(pokemon);
        //     poke.UpdateInfo(pokemon, details, hpStatus);
        // }


        // var position = BattleModel.BattlePokePosition.ParsePokeIdInfo(pokemon);
        IBattlePoke pokeModel = CreateOrUpdateBattlePoke(pokemon, details, hpStatus);
        Debug.Log($"SwitchBattlePoke CreateOrUpdateBattlePoke: {pokemon} {details}");
        Debug.Log($"SwitchBattlePoke pokeModel: {pokeModel.PokeIdInfo.siden} - {pokeModel.PokeIdInfo.slot}");
        // pokeModel.pokemonInfo.Active = true;
        //清空旧poke的位置index信息
        if (PlayerMap.TryGetValue(pokeModel.PokeIdInfo.siden, out var playerModel))
        {
            for (int i = 0; i < playerModel.Pokes.Count; i++)
            {
                var poke = playerModel.Pokes[i];
                if (poke != pokeModel && poke.PokeIdInfo.slot == pokeModel.PokeIdInfo.slot)
                {
                    // 在数组中找到目标 poke，交换数组位置
                    int pokeIndex = playerModel.Pokes.IndexOf(pokeModel);
                    if (pokeIndex >= 0)
                    {
                        var temp = playerModel.Pokes[i];
                        playerModel.Pokes[i] = playerModel.Pokes[pokeIndex];
                        playerModel.Pokes[pokeIndex] = temp;
                        // 交换数组中的位置
                        // (playerModel.Pokes[pokeIndex], playerModel.Pokes[i]) = (playerModel.Pokes[i], playerModel.Pokes[pokeIndex]);

                        // 更新两者的位置信息
                        // var tempIndex = pokeModel.position.index;
                        // poke.PokeIdInfo.slot = null;
                        poke.PokeIdInfo.ResetSlotInfo();
                        // pokeModel.position.index = tempIndex;

                        // 更新状态
                        // poke.SetActiveToFalse();
                    }
                    break;
                }
            }
        }
        // var pokeModel = GetPokeBy(pokemon, details, hpStatus);
        if (pokeModel != null)
        { //有些状态需要更新
            pokeModel.PokeStatus.RemoveVolatile(ConstEffectState.Volatiles.Itemremoved);
            var tox = pokeModel.PokeStatus.GetVolatile(ConstEffectState.Commonstatus.Tox);
            if (tox != null)
            {
                tox.Turns++;
            }
        }
        return pokeModel;
        // if (position.playerSideId != this.PlayerSideId)
        // {
        //     NinBattlerModel model = CreateOrUpdateBattlePoke(pokemon, details, hpStatus);
        //     model.pokemonInfo.Active = true;
        //     //清空旧poke的位置index信息
        //     if (PlayerMap.TryGetValue(model.position.playerSideIndex, out var playerModel))
        //     {
        //         foreach (var poke in playerModel.Pokes)
        //         {
        //             if (poke != model && poke.position != null && poke.position.index == model.position.index)
        //             {
        //                 poke.SetActiveToFalse();
        //             }
        //         }
        //     }
        //     return model;
        // }

        // poke.removeVolatile('itemremoved' as ID);
        // return pokeModel;
    }
    public IBattlePoke GetPokeBy(string pokemon, string? details = null, string? hpStatus = null)
    {
        var pokeIdInfo = BattlePokeIdInfo.ParsePokeIdInfo(pokemon);
        BattlePoke poke = new BattlePoke(pokemon);
        poke.UpdateInfo(pokemon, details, hpStatus);
        return GetPokeBy(pokeIdInfo, poke);
    }
    public IBattlePoke ChangePokeForme(string pokemon, string species, string hpStatus)
    {
        IBattlePoke model = GetPokeBy(pokemon, null, null);
        model.ChangeForme(species, hpStatus);
        return model;
    }
    // 通用方法，根据条件筛选
    private Dictionary<int, IBattlePoke[]> GetBattlerModels(Func<int, bool> filter, bool filterActive)
    {
        var list = new Dictionary<int, IBattlePoke[]>();
        foreach (var kvp in PlayerMap)
        {
            if (filter(kvp.Key))
            {
                var models = filterActive
                    ? kvp.Value.Pokes.Where(poke => (poke.IsActive)).OrderBy(poke => poke.PokeIdInfo.slot).ToArray()
                    : kvp.Value.Pokes.ToArray();
                list[kvp.Key] = models;
            }
        }
        return list;
    }
    public Dictionary<int, IBattlePoke[]> AllBattlerModels(bool filterActive = false)
    {
        return GetBattlerModels(key => true, filterActive);
    }
    public Dictionary<int, IBattlePoke[]> AllyBattlerModels(bool filterActive = false)
    {
        return GetBattlerModels(key => (key + playerSiden) % 2 == 0 && key != playerSiden, filterActive);
        // Dictionary<int, NinBattlerModel[]> kvModels = new();
        // foreach (var item in Pokes)
        // {
        //     if (item.Key == playerSideIndex) {
        //         continue;
        //     } else {
        //         var models = GetBattlerModels(key => (key + playerSideIndex) % 2 == 0 && key == item.Key, active).FirstOrDefault() ?? new NinBattlerModel[0];
        //         kvModels[item.Key] = models;
        //     }
        // }
        // return kvModels;
    }

    public IBattlePoke[] MyBattlerModels(bool filterActive = false)
    {
        return GetBattlerModels(key => key == playerSiden, filterActive).FirstOrDefault().Value ?? new BattlePoke[0];//简单粗暴的用%2取对方
    }
    public IBattlePoke[] GetFaintedBattlerModel(bool isMy)
    {
        return (GetBattlerModels(key => (isMy ? key == playerSiden : key != playerSiden), false).FirstOrDefault().Value ?? new BattlePoke[0]).Where(value => value.fainted).ToArray();
    }

    //获取对手的第一个Poke
    public IBattlePoke? FirstFoeBattlerModelBy(IBattlePoke model)
    {

        IBattlePoke[] models;
        if (IsMyAlly(model))
        {
            models = RightBattlerModels(true).FirstOrDefault().Value;
        }
        else
        {
            models = LeftBattlerModels(true).FirstOrDefault().Value;
        }
        if (models != null)
        {
            return models.FirstOrDefault();
        }
        return null;
    }
    // public List<NinBattlerModel[]> LeftCanTargetActiveBattlerModels() //是否可以作为选中目标 比如钻地和飞行状态 //或者道具 //或者捕捉
    // {
    //     return GetBattlerModels(key => (key + playerSideIndex) % 2 == 0, true);//简单粗暴的用%2取对方
    // }

    // public List<NinBattlerModel[]> RightCanTargetActiveBattlerModels() //是否可以作为选中目标 比如钻地和飞行状态 //或者道具 //或者捕捉
    // {
    //     return GetBattlerModels(key => (key + playerSideIndex) % 2 == 1, true);//简单粗暴的用%2取对方
    // }
    // public int IndexOnMyTeam(NinBattlerModel model) {
    //     var models = MyBattlerModels(false);
    //     for (int i = 0; i < models; i++)
    //     {

    //     }
    // }

    public Dictionary<int, IBattlePoke[]> LeftBattlerModels(bool filterActive = false)
    {
        return GetBattlerModels(key => (key + playerSiden) % 2 == 0, filterActive);//简单粗暴的用%2取对方
    }

    public Dictionary<int, IBattlePoke[]> RightBattlerModels(bool filterActive = false)
    {
        return GetBattlerModels(key => (key + playerSiden) % 2 == 1, filterActive);//简单粗暴的用%2取对方
    }

    // public List<NinBattlerModel[]> LeftTeamBatterModels()
    // {
    //     return GetBattlerModels(key => (key + playerSideIndex) % 2 == 0, false);//简单粗暴的用%2取对方
    // }

    // public List<NinBattlerModel[]> RightTeamBatterModels()
    // {
    //     return GetBattlerModels(key => (key + playerSideIndex) % 2 == 1, false);//简单粗暴的用%2取对方
    // }
    public bool BattleDecisionDone()
    {
        int count = _battleDecision.ChoiceCount();
        if (count == MyBattlerModels(true).Length)
        {
            return true;
        }
        return false;
    }
    public bool AddChoice(BattleChoice choice)
    {
        _battleDecision.AddChoice(choice);
        return true;
    }
    public bool Undo()
    {
        return _battleDecision.Undo();
    }
    public void SendBattleDecision(Action<bool> complete)
    {
        if (_battleDecision != null)
        {

            Debug.Log($"=========: {_battleDecision.ToString()}");
            if(GameContext.IsTestLocal || replay) {
                Debug.Log($"=========local or replay battle");
                _battleDecision = new();
                complete(true);
                actionExecutor.executeType = ExecuteType.Auto;
                return;
            }
            actionExecutor.BattleController.StartCoroutine(Client.ExecuteRpcTaskCoroutine(Client.Share.SendMatchStateAsync(_battleDecision.ToMessage()), (result) =>
            {
                if (result.Success)
                {
                    Debug.Log($"BattleChoiceMesssage 执行完成: {result}");
                    Debug.Log($"BattleChoiceMesssage actionExecutor.IsActionExecuteEnd(): {actionExecutor.IsActionExecuteEnd()}");

                    _battleDecision = new();
                    complete(true);
                }
                else
                {
                    Debug.Log($"BattleChoiceMesssage 错误: {result}");
                    _battleDecision = new();
                    complete(false);
                }
            }));
            // actionExecutor.BattleController.StartCoroutine(Client.ExecuteRpcTaskCoroutine(Client.Share.BattleChoiceMesssage(_battleDecision), (result) =>
            // {
            //     if (result.Success)
            //     {
            //         Debug.Log($"BattleChoiceMesssage 执行完成: {result}");
            //         Debug.Log($"BattleChoiceMesssage actionExecutor.IsActionExecuteEnd(): {actionExecutor.IsActionExecuteEnd()}");

            //         _battleDecision = new();
            //         complete(true);
            //     }
            //     else
            //     {
            //         Debug.Log($"BattleChoiceMesssage 错误: {result}");
            //         _battleDecision = new();
            //         complete(false);
            //     }
            // }));
        }
        if(GameContext.IsTestLocal == true || replay) {
            actionExecutor.executeType = ExecuteType.Auto;
        }
    }
    // public List<NinBattlerModel[]> LeftActiveBattleModels()
    // {
    //     var list = new List<NinBattlerModel[]>();
    //     foreach (var kvp in Pokes)
    //     {
    //         if((kvp.Key + playerSideIndex) % 2 == 0) {
    //             list.Add(kvp.Value.Where(poke => poke.pokemonInfo.Active).ToArray());
    //         }
    //     }
    //     return list;
    // }
    // public List<NinBattlerModel[]> RightActiveBattleModels()
    // {
    //     var list = new List<NinBattlerModel[]>();
    //     foreach (var kvp in Pokes)
    //     {
    //         if((kvp.Key + playerSideIndex) % 2 == 1) { //简单粗暴的用%2取对方
    //             list.Add(kvp.Value.Where(poke => poke.pokemonInfo.Active).ToArray());
    //         }
    //     }
    //     return list;
    // }
    // public List<NinBattlerModel[]> LeftTeamBatterModels() {
    //     var list = new List<NinBattlerModel[]>();
    //     foreach (var kvp in Pokes)
    //     {
    //         if((kvp.Key + playerSideIndex) % 2 == 0) {
    //             list.Add(kvp.Value.ToArray());
    //         }
    //     }
    //     return list;
    // }
    // public List<NinBattlerModel[]> RightTeamBatterModels() {
    //     var list = new List<NinBattlerModel[]>();
    //     foreach (var kvp in Pokes)
    //     {
    //         if((kvp.Key + playerSideIndex) % 2 == 1) { //简单粗暴的用%2取对方
    //             list.Add(kvp.Value.ToArray());
    //         }
    //     }
    //     return list;
    // }
    // addPokemon
}
