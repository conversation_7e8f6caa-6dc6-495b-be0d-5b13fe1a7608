using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Cysharp.Threading.Tasks;
using PetsServices;
using UnityEngine;
//战场中的
public class NinBattleFieldPokeController : MonoBehaviour
{
    public BattleMoverAnimator battleMoverAnimator;
    // public BallsPokes
    public NinPokeBattler battler1Prefab;//不同的filpx
    // public NinPokeBattler battler2Prefab;//不同的filpx
    public NinPokeBattler battlerTrainer1Prefab;//不同的filpx
    public NinPokeBattler battlerTrainer2Prefab;//不同的filpx

    //GAMETYPE is singles, doubles, triples, multi, or freeforall.
    List<NinPokeBattler> ninPokeBattlersLeft = new();
    List<NinPokeBattler> ninPokeBattlersRight = new();
    public BattleContext battleContext;

    public int SideLength = 1;
    public async UniTask PokemonFainted(INinBattlerModel pokeModel) {
        var model = pokeModel as PsBattleModel.IBattlePoke;
        if(model != null) {
            var battler = GetBattlerBy(model.PokeIdInfo);
            if(battler == null) {
                PLog.Error($"NinBattleFieldPokeController PokemonFainted not found: {model.PokeIdInfo.siden} {model.PokeIdInfo.slot} {model.PokeIdInfo.pokeId}");
            }
            await battler.Fainted();
        }
    }
    public async UniTask SwitchPokemon(INinBattlerModel pokeModel) {
        // var pokeModel = battleContext.SwitchBattlePoke(pokemon, details, hpStatus);
        var model = pokeModel as PsBattleModel.IBattlePoke;
        if(model != null) {
            var battler = GetBattlerBy(model.PokeIdInfo);
            if(battler == null) {
                PLog.Error($"NinBattleFieldPokeController SwitchPokemon not found: {model.PokeIdInfo.siden} {model.PokeIdInfo.slot} {model.PokeIdInfo.pokeId}");
                return;
            }
            // var pokemonSpecies = await pokeModel.PokemonSpecies();
            battleContext.actionExecutor.ShowMessage($"{model.NameLocalized}出战");
            if(battleContext.TargetType == BattleTargetType.Wild && !battler.isLeft) {
                await battler.EncounterBattler(model);
            } else {
                await battler.SwitchBattler(model);
            }
        }
    }
    public async UniTask UpdateBattleForme(INinBattlerModel pokeModel) {
        var model = pokeModel as PsBattleModel.IBattlePoke;
        if(model != null) {
            var battler = GetBattlerBy(model.PokeIdInfo);
            if(battler == null) {
                PLog.Error($"NinBattleFieldPokeController SwitchPokemon not found: {model.PokeIdInfo.siden} {model.PokeIdInfo.slot} {model.PokeIdInfo.pokeId}");
                return;
            }
            await battler.EncounterBattler(model);
            // var pokemonSpecies = await pokeModel.PokemonSpecies();
            // battleContext.actionExecutor.ShowMessage($"{model.NameLocalized}出战");
            // if(battleContext.TargetType == BattleTargetType.Wild && !battler.isLeft) {
            // } else {
            //     await battler.SwitchBattler(model);
            // }
        }
    }
    public void UseMove(NinBattleMoveModel moveModel, Action complete) {
        // Debug.Log($"NinBattleFieldPokeController UseMove: {pokemon}, {moveName}");
        var model = moveModel.pokeModel as PsBattleModel.IBattlePoke;
        if(model == null) {
            PLog.Error($"NinBattleFieldPokeController UseMove pokeModel: null");
            complete();
            return;
        }
        // NinBattlerModel targetModel = moveModel.targetModels.FirstOrDefault();
        List<NinPokeBattler> targetBattlers = new(); //有些招式是没有目标的
        // if(battleContext.SideBattlerNumber == 1 && moveModel.targetModels.Length <= 0) {
        //     targetModel = battleContext.FirstFoeBattlerModelBy(pokeModel);
        //     if(targetModel != null) {
        //         targetBattler = GetBattlerBy(targetModel.position);
        //     }
        // } else if(!string.IsNullOrEmpty(target)) {
        //     targetModel = battleContext.GetBattlePokeBy(target, null, null);
        //     targetBattler = GetBattlerBy(targetModel.position);
        // }
        foreach (var targetModel in moveModel.targetModels)
        {
            var pmodel = targetModel as PsBattleModel.IBattlePoke;
            if(pmodel != null) {
                var targetBattler = GetBattlerBy(pmodel.PokeIdInfo);
                if(targetBattler != null) {
                    targetBattlers.Add(targetBattler);
                }
            }
        }
       
        var battler = GetBattlerBy(model.PokeIdInfo);
        if(battler == null) {
            PLog.Error($"NinBattleFieldPokeController PokemonFainted not found: {model.PokeIdInfo.siden} {model.PokeIdInfo.slot} {model.PokeIdInfo.pokeId}");
        }
        // if(suppressAnimation) {
        //     Move move;
        //     if(animMove != null) {
        //         move = PokeDataLoad.Share.GetIndexData<Move>(animMove).Result;
        //     } else {
        //         move = PokeDataLoad.Share.GetIndexData<Move>(name).Result;
        //     }
            
        // }
        // string target, bool missed, 
        // target, missed, 
        battler.UseMove(moveModel, () => {
            // if(move != null) {
                Debug.Log("准备播放Move" + moveModel.NameLocalized);
                //灵骚	ポルターガイスト	Poltergeist	幽灵	物理	110	90	5	操纵对手的持有物进行攻击。当对手没有携带道具时，使出此招式时便会失败。
                //这个地方有问题
                // NinBattleMoveModel moveModel = new(move, missed);
                // moveModel.source = battler;
                // moveModel.target = targetBattler;
                battleMoverAnimator.PlayMove(moveModel, targetBattlers.ToArray(), () => {
                    complete();
                    // if(targetBattler != null) {
                    //     //missed 可以通过这个判断播放受击动画，还是躲避动画
                    //     //已经通过触发器判断
                    //     // targetBattler.UnderAttack(complete);
                    // } else {
                    //     complete();
                    // }
                });
            // } else {
            //     Debug.Log("没有攻击动画？");
            //     complete();
            // }
        });
    }
    public async UniTask Dynamax(INinBattlerModel pokeModel) {
        var model = pokeModel as PsBattleModel.IBattlePoke;
        if(model == null) {
            PLog.Error($"NinBattleFieldPokeController Dynamax pokeModel: null");
            return;
        }
        var battler = GetBattlerBy(model.PokeIdInfo);
        if(battler == null) {
            PLog.Error($"NinBattleFieldPokeController Dynamax not found: {model.PokeIdInfo.siden} {model.PokeIdInfo.slot} {model.PokeIdInfo.pokeId}");
        }
        await battler.PlayerSpecialMove(MainServer.BattleSpecialMove.SpecialMoveMax);
    }
    public async UniTask Tera(INinBattlerModel pokeModel) {
        var model = pokeModel as PsBattleModel.IBattlePoke;
        if(model == null) {
            PLog.Error($"NinBattleFieldPokeController Tera pokeModel: null");
            return;
        }
        var battler = GetBattlerBy(model.PokeIdInfo);
        if(battler == null) {
            PLog.Error($"NinBattleFieldPokeController Tera not found: {model.PokeIdInfo.siden} {model.PokeIdInfo.slot} {model.PokeIdInfo.pokeId}");
        }
        await battler.PlayerSpecialMove(MainServer.BattleSpecialMove.SpecialMoveTerastallize);
    }
    public async UniTask Zmove(INinBattlerModel pokeModel) {
        var model = pokeModel as PsBattleModel.IBattlePoke;
        if(model == null) {
            PLog.Error($"NinBattleFieldPokeController Zmove pokeModel: null");
            return;
        }
        var battler = GetBattlerBy(model.PokeIdInfo);
        if(battler == null) {
            PLog.Error($"NinBattleFieldPokeController Zmove not found: {model.PokeIdInfo.siden} {model.PokeIdInfo.slot} {model.PokeIdInfo.pokeId}");
        }
        await battler.PlayerSpecialMove(MainServer.BattleSpecialMove.SpecialMoveZmove);
    }
    public async UniTask Mega(INinBattlerModel pokeModel) {
        var model = pokeModel as PsBattleModel.IBattlePoke;
        if(model == null) {
            PLog.Error($"NinBattleFieldPokeController Mega pokeModel: null");
            return;
        }
        var battler = GetBattlerBy(model.PokeIdInfo);
        if(battler == null) {
            PLog.Error($"NinBattleFieldPokeController Mega not found: {model.PokeIdInfo.siden} {model.PokeIdInfo.slot} {model.PokeIdInfo.pokeId}");
        }
        await battler.PlayerSpecialMove(MainServer.BattleSpecialMove.SpecialMoveMega);
        // var battler = GetBattlerBy(battlerModel.PokeIdInfo);
        // if(battler == null) {
        //     PLog.Error($"NinBattleFieldPokeController Mega not found: {battlerModel.PokeIdInfo.siden} {battlerModel.PokeIdInfo.slot} {battlerModel.PokeIdInfo.pokeId}");
        // }
    }
    public void ClearPoke()
    {
        ninPokeBattlersLeft.ForEach((battler) =>
        {
            Destroy(battler.gameObject);
        });
        ninPokeBattlersRight.ForEach((battler) =>
        {
            Destroy(battler.gameObject);
        });
        ninPokeBattlersLeft = new();
        ninPokeBattlersRight = new();
    }
    public void AddBattleObject(string gameType, BattleContext battleContext)
    {
        this.battleContext = battleContext;
        //或者通过active的poke数量
        int length = gameType switch
        {
            "singles" => 1,
            "doubles" => 2,
            "gameType" => 3,
            _ => 1
        };
        SideLength = length;
        // int length = 1;
        // if (gameType == "singles")
        // {
        //     length = 1;
        // }
        // else if (gameType == "doubles")
        // {
        //     length = 2;
        // }
        // else if (gameType == "gameType")
        // {
        //     length = 3;
        // }
        // 4.5 , 0, -6 | 0, 0, -6
        // 4.5 , 0, 6 | 0, 0, 6
        var zValue = 6f;
        if(NinSettingConfig.Instance.ClassicCamera) {
            zValue = 10f;
        }
        Vector3 leftPosition = new Vector3(4.5f, 0, -zValue);
        Vector3 rightPosition = new Vector3(4.5f, 0, zValue);
        // for (int i = 0; i < length; i++)
        // Vector3 leftPosition = battler1Prefab.gameObject.transform.localPosition;
        // Vector3 rightPosition = battler2Prefab.gameObject.transform.localPosition;
        for (int i = 0; i < length; i++)
        {
            Vector3 position1 = leftPosition;
            position1.x = leftPosition.x - i * leftPosition.x;
            position1.y = leftPosition.y;
            position1.z = leftPosition.z;
            // Debug.Log($"position1: {position1}");
            NinPokeBattler battler1 = Instantiate(battler1Prefab, transform);
            battler1.UpdateFaceCamera();
            battler1.transform.localPosition = position1; // 使用本地位置设置
            battler1.gameObject.SetActive(true);
            battler1.SetOnDirection(true);
            // battler1.animator.pokeAnimator.flipX = true;
            ninPokeBattlersLeft.Add(battler1);

            Vector3 position2 = rightPosition;
            position2.x = rightPosition.x - i * rightPosition.x;
            position2.y = rightPosition.y;
            position2.z = rightPosition.z;
            // Debug.Log($"position2: {position2}");
            NinPokeBattler battler2 = Instantiate(battler1Prefab, transform);
            // battler2.animator.pokeAnimator.flipX = true;
            // battler2.Animator.SetSpriteFlip(false, false); 因为使用 SetSpriteFlip的时候 spriteRenderer 还是null的
            battler2.transform.localPosition = position2; // 使用本地位置设置
            battler2.gameObject.SetActive(true);
            battler2.SetOnDirection(false);
            battler2.UpdateFaceCamera();
            ninPokeBattlersRight.Add(battler2);
        }
    }
    // public void AddBattleObject(int lineCount) {
    //     ninPokeBattlers
    // }
    // public void SwitchPokemon(NinBattlerModel battler)
    // {
    //     //精灵出厂动画
    // }
    // // Start is called before the first frame update
    void Start()
    {
        DynamicGI.UpdateEnvironment();
    }

    // void 

    // public void StartBattle() {
    //     Debug.Log("StartBattle");
    //     pokeEA?.BattleBall.Parabolic();
    //     pokeEB?.BattleBall.Parabolic();
    //     pokeMA?.BattleBall.Parabolic();
    //     pokeMB?.BattleBall.Parabolic();
    // }

    // Update is called once per frame
    void Update()
    {

    }
    public NinPokeBattler? GetBattlerBy(PsBattleModel.BattlePokeIdInfo pokeIdInfo)
    {
        // 解析位置信息
        // var position = BattleModel.BattlePokePosition.ParseBattlePokePosition(sideInfo);
        // Debug.Log($"NinBattleFieldPokeController GetBattlerBy1: {position.playerSideId} - {position.index}");
        // 检查无效位置
        if (pokeIdInfo.siden < 0 || pokeIdInfo.slot == null)
            return null;
        var isRight = pokeIdInfo.siden % 2 != battleContext.playerSiden % 2;
        // 根据条件选择目标列表 //玩家视角
        List<NinPokeBattler> targetList = isRight
                ? ninPokeBattlersRight
                : ninPokeBattlersLeft.AsEnumerable().Reverse().ToList(); //因为战斗poke的布局是前到后的，所以left需要反序
        // Debug.Log($"NinBattleFieldPokeController GetBattlerBy2: {position.playerSideId} - {position.playerSideIndex} - {position.slot}");
        // 计算索引
        // int index = (position.playerSideIndex - 1) / 2;
        int slot = (int)pokeIdInfo.slot;
        // if(position.playerIndex) //索引计算不对 a，b，c要映射 ，通过gametype
        // 检查索引是否在范围内
        if (slot < 0 || slot >= targetList.Count)
            return null;
        var targetBattler = targetList[slot];
        // Debug.Log($"NinBattleFieldPokeController GetBattlerBy3: {position.playerSideId} - {slot}");
        // 返回目标对象
        return targetBattler;
    }
}
