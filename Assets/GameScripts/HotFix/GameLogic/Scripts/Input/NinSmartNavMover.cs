using UnityEngine;
using UnityEngine.AI;
using System.Collections;
using System;
using Cysharp.Threading.Tasks;

public class NinSmartNavMover: MonoBehaviour
{
    [Header("移动参数")]
    public float moveSpeed = 3f;           // 基础速度
    public float closeDistance = 0.5f;     // 插值距离
    public float midDistance = 10f;        // NavMesh 距离
    public float teleportDistance = 30f;   // 超过这个距离瞬移
    public float stoppingDistance = 0.1f;  // 最终停止距离
    private NinChatacterTransfer _ninChatacterTransfer;
    private NavMeshAgent agent;
    private Coroutine moveRoutine;
    private Vector3 _currentTargetPosition;
    private bool isMoving = false;
    public bool IsMoving => isMoving;

    // public Action OnArrived; // ✅ 到达目标的回调

    // private enum MoveMode { None, Lerp, NavMesh, Teleport }
    // private MoveMode currentMode = MoveMode.None;

    // 回调
    public Action OnArrivedAll; // 所有路径到达
    public Action<int> OnArrivedPoint; // 每个路径点到达，参数为索引
    // [Header("配置参数")]
    // private float closeDistance = 3f;        // 近距离插值平滑
    // private float midDistance = 7f;           // 中距离用NavMesh
    // private float farDistance = 50f;          // 超远瞬移
    // private float moveSpeed = 3f;           // 插值速度
    // private float _flowDistance = 1f;

    // private NavMeshAgent agent;
    // private Coroutine moveRoutine;
    // private bool isMoving = false;
    // private Vector3 _currentTargetPosition = Vector3.zero;

    void Awake()
    {
        _ninChatacterTransfer = this.gameObject.GetComponentInChildren<NinChatacterTransfer>();
        agent = GetComponent<NavMeshAgent>();
        if (agent == null)
        {
            agent = gameObject.AddComponent<NavMeshAgent>();
        }
        agent.speed = moveSpeed;
        agent.updateRotation = true;
    }
    public void SetMoveSpeed(float speed) {
        moveSpeed = speed;
        agent.speed = moveSpeed;
    }
    public Vector3 GetMoveDirection() {
        if(isMoving) {
            return _currentTargetPosition - transform.position;
        }
        return Vector3.zero;
    }
    /// <summary>
    /// 开始沿路径移动
    /// </summary>
    // public void MoveAlongPath(Vector3[] points)
    // {
    //     StopMoving();

    //     if (points == null || points.Length == 0)
    //     {
    //         Debug.LogWarning("路径为空，无法移动");
    //         return;
    //     }

    //     moveRoutine = StartCoroutine(MoveRoutine(points));
    // }

    public void SetNewTargetLocs(MainServer.TrainerLoc[] positions)
    {
        // _targetPositions.AddRange(positions.ToList());
        // if (_targetPositions.Count > 0)
        // {
        //     var nextTarget = _targetPositions[0];
        //     _targetPositions.RemoveAt(0);
        //     SetNewTargetLoc(nextTarget);
        // }
    }
    public Vector3 GetCurrentTargetPosition() {
        return _currentTargetPosition;
        // return transform.forward;
    }
    /// <summary>
    /// 移动到单个点
    /// </summary>
    public void MoveToPosition(Vector3 position)
    {
        MoveAlongPath(new Vector3[] { position });
    }

    /// <summary>
    /// 按路径移动
    /// </summary>
    public void MoveAlongPath(Vector3[] pathPoints)
    {
        StopMoving();
        moveRoutine = StartCoroutine(MoveRoutine(pathPoints));
    }

    /// <summary>
    /// 停止移动
    /// </summary>
    public void StopMoving()
    {
        if (moveRoutine != null)
        {
            StopCoroutine(moveRoutine);
            moveRoutine = null;
        }

        if (agent.enabled)
        {
            agent.isStopped = true;
        }

        isMoving = false;
    }

    /// <summary>
    /// 主协程：沿路径依次移动
    /// </summary>
    private IEnumerator MoveRoutine(Vector3[] points)
    {
        isMoving = true;

        for (int i = 0; i < points.Length; i++)
        {
            Vector3 target = points[i];
            _currentTargetPosition = target;

            while (true)
            {
                float distance = Vector3.Distance(transform.position, target);
                // if(distance < stoppingDistance) {
                //     break;
                // }
                // distance = Vector3.Distance(transform.position, target);
                if (distance <= stoppingDistance)
                {
                    OnArrivedPoint?.Invoke(i);
                    break; // 继续下一个点
                }

                if (distance > closeDistance && distance < midDistance )
                {
                    yield return StartCoroutine(LerpToPosition(target));
                    break;
                }
                else if (distance >= midDistance && distance < teleportDistance)
                {
                    yield return StartCoroutine(NavMeshMove(target));
                    break;
                }
                else if (distance >= teleportDistance)
                {
                    // transform.position = target;
                    _ninChatacterTransfer.Transfer(target).Forget();
                    yield return null; // 给一帧
                    break;
                }

                yield return null;
            }
        }

        isMoving = false;
        OnArrivedAll?.Invoke();
    }

    /// <summary>
    /// 插值平滑移动
    /// </summary>
    private IEnumerator LerpToPosition(Vector3 target)
    {
        while (Vector3.Distance(transform.position, target) > stoppingDistance)
        {
            transform.position = Vector3.MoveTowards(transform.position, target, moveSpeed * Time.deltaTime);
            yield return null;
        }
        transform.position = target;
    }

    /// <summary>
    /// 使用 NavMeshAgent 移动
    /// </summary>
    private IEnumerator NavMeshMove(Vector3 target)
    {
        agent.isStopped = false;
        agent.speed = moveSpeed * 1.2f; // 跟随时稍快
        agent.SetDestination(target);

        float timeout = 10f;
        float timer = 0f;

        while ((agent.pathPending || agent.remainingDistance > agent.stoppingDistance) && timer < timeout)
        {
            timer += Time.deltaTime;
            yield return null;
        }

        agent.isStopped = true;
    }

}