using UnityEngine;
using UnityEngine.AI;
using System;
using Cysharp.Threading.Tasks;
using System.Threading;
using System.Collections.Generic;

/// <summary>
/// 智能导航移动器，支持多种移动方式和队列移动系统
///
/// 使用示例：
/// // 即时移动
/// mover.MoveToPosition(targetPos);
///
/// // 队列移动
/// mover.AddMoveToPosition(pos1);
/// mover.AddMoveToPosition(pos2);
/// mover.AddMoveToPositions(new Vector3[] { pos3, pos4, pos5 });
///
/// // 队列管理
/// mover.ClearMoveQueue();
/// mover.StopQueueProcessing();
/// int remaining = mover.GetQueueCount();
/// </summary>
public class NinSmartNavMover: MonoBehaviour
{
    [Header("移动参数")]
    public float moveSpeed = 3f;           // 基础速度
    public float closeDistance = 3f;       // 近距离用插值（≤此距离）
    public float teleportDistance = 30f;   // 超过此距离瞬移（>此距离）
    public float stoppingDistance = 0.1f;  // 最终停止距离
    public bool useSmoothLerp = true;      // 是否使用平滑插值（更顺滑但稍慢）
    // 中距离（closeDistance < 距离 ≤ teleportDistance）使用NavMesh
    private NinChatacterTransfer _ninChatacterTransfer;
    private NavMeshAgent agent;
    private CancellationTokenSource moveCts;
    private Vector3 _currentTargetPosition = Vector3.zero;
    // private Vector3 _lastTargetPosition = Vector3.zero; // 记录最后一次的目标位置
    private bool isMoving = false;
    public bool IsMoving => isMoving;

    // 队列移动系统
    private readonly Queue<Vector3> _moveQueue = new();
    private bool _isQueueProcessing = false;
    private CancellationTokenSource _queueCts;

    // public Action OnArrived; // ✅ 到达目标的回调

    // private enum MoveMode { None, Lerp, NavMesh, Teleport }
    // private MoveMode currentMode = MoveMode.None;

    // 回调
    public Action OnArrivedAll; // 所有路径到达
    public Action<int> OnArrivedPoint; // 每个路径点到达，参数为索引
    public Action<Vector3> OnQueuePositionArrived; // 队列中某个位置到达
    public Action OnQueueCompleted; // 整个队列完成
    // [Header("配置参数")]
    // private float closeDistance = 3f;        // 近距离插值平滑
    // private float midDistance = 7f;           // 中距离用NavMesh
    // private float farDistance = 50f;          // 超远瞬移
    // private float moveSpeed = 3f;           // 插值速度
    // private float _flowDistance = 1f;

    // private NavMeshAgent agent;
    // private Coroutine moveRoutine;
    // private bool isMoving = false;
    // private Vector3 _currentTargetPosition = Vector3.zero;

    void Awake()
    {
        _ninChatacterTransfer = this.gameObject.GetComponentInChildren<NinChatacterTransfer>();
        agent = GetComponent<NavMeshAgent>();
        if (agent == null)
        {
            agent = gameObject.AddComponent<NavMeshAgent>();
        }
        agent.speed = moveSpeed;
        agent.updateRotation = true;
    }

    void OnDestroy()
    {
        // 确保在对象销毁时取消移动任务和队列处理
        StopMoving();
        StopQueueProcessing();
    }
    public void SetMoveSpeed(float speed) {
        moveSpeed = speed;
        agent.speed = moveSpeed;
    }
    public Vector3 GetMoveDirection() {
        if(isMoving) {
            return _currentTargetPosition - transform.position;
        }
        return Vector3.zero;
    }
    /// <summary>
    /// 开始沿路径移动
    /// </summary>
    // public void MoveAlongPath(Vector3[] points)
    // {
    //     StopMoving();

    //     if (points == null || points.Length == 0)
    //     {
    //         Debug.LogWarning("路径为空，无法移动");
    //         return;
    //     }

    //     moveRoutine = StartCoroutine(MoveRoutine(points));
    // }

    public void SetNewTargetLocs(MainServer.TrainerLoc[] positions)
    {
        // _targetPositions.AddRange(positions.ToList());
        // if (_targetPositions.Count > 0)
        // {
        //     var nextTarget = _targetPositions[0];
        //     _targetPositions.RemoveAt(0);
        //     SetNewTargetLoc(nextTarget);
        // }
    }
    public Vector3 GetCurrentTargetPosition() {
        return _currentTargetPosition;
        // return transform.forward;
    }

    /// <summary>
    /// Transfer完成后调用，同步NavMeshAgent状态
    /// </summary>
    public void OnTransferCompleted()
    {
        if (agent != null && agent.enabled)
        {
            agent.ResetPath();
            agent.isStopped = true;
            agent.Warp(transform.position);
        }
        // 不重置_currentTargetPosition，保持当前目标
    }

    #region 队列移动系统

    /// <summary>
    /// 添加位置到移动队列
    /// </summary>
    /// <param name="position">目标位置</param>
    public void AddMoveToPosition(Vector3 position)
    {
        _moveQueue.Enqueue(position);

        // 如果队列处理器没有运行，启动它
        if (!_isQueueProcessing)
        {
            StartQueueProcessing().Forget();
        }
    }

    /// <summary>
    /// 添加多个位置到移动队列
    /// </summary>
    /// <param name="positions">位置数组</param>
    public void AddMoveToPositions(Vector3[] positions)
    {
        foreach (var position in positions)
        {
            _moveQueue.Enqueue(position);
        }

        // 如果队列处理器没有运行，启动它
        if (!_isQueueProcessing)
        {
            StartQueueProcessing().Forget();
        }
    }

    /// <summary>
    /// 清空移动队列
    /// </summary>
    public void ClearMoveQueue()
    {
        _moveQueue.Clear();
        Debug.Log("移动队列已清空");
    }

    /// <summary>
    /// 停止队列处理
    /// </summary>
    public void StopQueueProcessing()
    {
        if (_queueCts != null)
        {
            _queueCts.Cancel();
            _queueCts.Dispose();
            _queueCts = null;
        }
        _isQueueProcessing = false;
        StopMoving(); // 停止当前移动
        Debug.Log("队列处理已停止");
    }

    /// <summary>
    /// 获取队列中剩余的位置数量
    /// </summary>
    public int GetQueueCount()
    {
        return _moveQueue.Count;
    }

    /// <summary>
    /// 检查队列是否正在处理
    /// </summary>
    public bool IsQueueProcessing => _isQueueProcessing;

    /// <summary>
    /// 检查队列是否为空
    /// </summary>
    public bool IsQueueEmpty => _moveQueue.Count == 0;

    /// <summary>
    /// 获取队列中的所有位置（只读）
    /// </summary>
    public Vector3[] GetQueuePositions()
    {
        return _moveQueue.ToArray();
    }

    /// <summary>
    /// 立即移动到位置（不使用队列）
    /// </summary>
    /// <param name="position">目标位置</param>
    public void MoveToPositionImmediate(Vector3 position)
    {
        // 停止队列处理
        StopQueueProcessing();
        // 使用原有的即时移动方法
        MoveToPosition(position);
    }

    /// <summary>
    /// 启动队列处理
    /// </summary>
    private async UniTask StartQueueProcessing()
    {
        if (_isQueueProcessing)
        {
            return; // 已经在处理中
        }

        _isQueueProcessing = true;
        _queueCts = new CancellationTokenSource();
        var token = _queueCts.Token;

        try
        {
            while (_moveQueue.Count > 0 && !token.IsCancellationRequested)
            {
                Vector3 nextPosition = _moveQueue.Dequeue();

                Debug.Log($"队列移动到位置: {nextPosition}, 剩余队列: {_moveQueue.Count}");

                // 移动到下一个位置
                await MoveToPositionAsync(nextPosition, token);

                // 触发位置到达回调
                OnQueuePositionArrived?.Invoke(nextPosition);

                // 等待一小段时间，避免过于频繁的移动
                await UniTask.Delay(50, DelayType.DeltaTime, PlayerLoopTiming.Update, token);
            }

            // 队列完成回调
            if (_moveQueue.Count == 0)
            {
                OnQueueCompleted?.Invoke();
                Debug.Log("队列移动全部完成");
            }
        }
        catch (OperationCanceledException)
        {
            Debug.Log("队列处理被取消");
        }
        finally
        {
            _isQueueProcessing = false;
            if (_queueCts != null)
            {
                _queueCts.Dispose();
                _queueCts = null;
            }
            Debug.Log("队列处理完成");
        }
    }

    /// <summary>
    /// 异步移动到指定位置（队列专用）
    /// </summary>
    private async UniTask MoveToPositionAsync(Vector3 position, CancellationToken token)
    {
        _currentTargetPosition = position;

        // 直接根据距离选择移动方式
        float distance = Vector3.Distance(transform.position, position);

        if (distance <= stoppingDistance)
        {
            // 已经到达目标
            return;
        }
        else if (distance > teleportDistance)
        {
            // 超远距离：使用Transfer瞬移
            if (agent != null && agent.enabled)
            {
                agent.ResetPath();
                agent.isStopped = true;
            }

            await _ninChatacterTransfer.Transfer(position);

            // Transfer完成后，确保NavMeshAgent状态正确
            await UniTask.Delay(100, DelayType.DeltaTime, PlayerLoopTiming.Update, token);
            if (agent != null && agent.enabled)
            {
                agent.Warp(transform.position);
            }
        }
        else if (distance <= closeDistance)
        {
            // 近距离：使用插值移动
            await LerpToPositionAsync(position, token);
        }
        else
        {
            // 中远距离：使用NavMesh移动
            await NavMeshMoveAsync(position, token);
        }
    }

    #endregion
    /// <summary>
    /// 移动到单个点
    /// </summary>
    private void MoveToPosition(Vector3 position)
    {
        // 如果正在移动且距离很近，直接更新目标位置而不重新开始移动
        if (isMoving && Vector3.Distance(position, _currentTargetPosition) < closeDistance)
        {
            _currentTargetPosition = position;
            Debug.Log("更新移动目标位置");
            return;
        }

        // 检查位置是否与上次完全相同
        if (Vector3.Distance(position, _currentTargetPosition) < 0.01f && isMoving)
        {
            Debug.Log("目标位置与上次相同且正在移动中，跳过移动");
            return;
        }

        MoveAlongPath(new Vector3[] { position });
    }

    /// <summary>
    /// 按路径移动
    /// </summary>
    public void MoveAlongPath(Vector3[] pathPoints)
    {
        StopMoving();
        MoveRoutineAsync(pathPoints).Forget();
    }

    /// <summary>
    /// 停止移动
    /// </summary>
    public void StopMoving()
    {
        if (moveCts != null)
        {
            moveCts.Cancel();
            moveCts.Dispose();
            moveCts = null;
        }

        if (agent != null && agent.enabled)
        {
            agent.isStopped = true;
            agent.ResetPath(); // 清除NavMeshAgent的路径
        }

        isMoving = false;
        _currentTargetPosition = Vector3.zero; // 重置当前目标位置
    }

    /// <summary>
    /// 主移动任务：沿路径依次移动
    /// </summary>
    private async UniTask MoveRoutineAsync(Vector3[] points)
    {
        moveCts = new CancellationTokenSource();
        var token = moveCts.Token;

        try
        {
            isMoving = true;

            for (int i = 0; i < points.Length; i++)
            {
                Vector3 target = points[i];
                _currentTargetPosition = target;

                // 直接根据距离选择移动方式，不使用循环
                float distance = Vector3.Distance(transform.position, target);

                if (distance <= stoppingDistance)
                {
                    // 已经到达目标
                    OnArrivedPoint?.Invoke(i);
                }
                else if (distance > teleportDistance)
                {
                    // 超远距离：使用Transfer瞬移
                    if (agent != null && agent.enabled)
                    {
                        agent.ResetPath();
                        agent.isStopped = true;
                    }

                    await _ninChatacterTransfer.Transfer(target);

                    // Transfer完成后，确保NavMeshAgent状态正确
                    await UniTask.Delay(100, DelayType.DeltaTime, PlayerLoopTiming.Update, token);
                    if (agent != null && agent.enabled)
                    {
                        agent.Warp(transform.position);
                    }
                    OnArrivedPoint?.Invoke(i);
                }
                else if (distance <= closeDistance)
                {
                    // 近距离：使用插值移动
                    await LerpToPositionAsync(target, token);
                    OnArrivedPoint?.Invoke(i);
                }
                else
                {
                    // 中远距离：使用NavMesh移动
                    await NavMeshMoveAsync(target, token);
                    OnArrivedPoint?.Invoke(i);
                }
            }

            isMoving = false;
            OnArrivedAll?.Invoke();
        }
        catch (OperationCanceledException)
        {
            // 移动被取消
            isMoving = false;
        }
        finally
        {
            if (moveCts != null)
            {
                moveCts.Dispose();
                moveCts = null;
            }
        }
    }

    /// <summary>
    /// 插值平滑移动
    /// </summary>
    private async UniTask LerpToPositionAsync(Vector3 target, CancellationToken token)
    {
        float timeout = 10f; // 超时保护
        float timer = 0f;

        while (timer < timeout)
        {
            token.ThrowIfCancellationRequested();

            // 对于队列移动，使用传入的target；对于实时移动，使用_currentTargetPosition
            Vector3 currentTarget = _isQueueProcessing ? target : _currentTargetPosition;
            float distance = Vector3.Distance(transform.position, currentTarget);

            // 如果已经到达目标，退出
            if (distance <= stoppingDistance)
            {
                break;
            }

            float deltaTime = Time.deltaTime;
            timer += deltaTime;

            if (useSmoothLerp)
            {
                // 使用平滑插值，更自然的加速减速效果
                float lerpSpeed = moveSpeed * 0.8f; // 调整插值速度，提高响应性
                transform.position = Vector3.Lerp(transform.position, currentTarget, lerpSpeed * deltaTime);
            }
            else
            {
                // 使用匀速移动，更直接
                transform.position = Vector3.MoveTowards(transform.position, currentTarget, moveSpeed * deltaTime);
            }

            // 每帧更新，保证最大的流畅度
            await UniTask.Yield(PlayerLoopTiming.Update, token);
        }

        // 确保到达目标位置（但不强制设置，避免突然跳跃）
        if (timer >= timeout)
        {
            Debug.LogWarning("插值移动超时");
        }
    }

    /// <summary>
    /// 使用 NavMeshAgent 移动
    /// </summary>
    private async UniTask NavMeshMoveAsync(Vector3 target, CancellationToken token)
    {
        if (agent == null || !agent.enabled)
        {
            Debug.LogWarning("NavMeshAgent 不可用，切换到插值移动");
            await LerpToPositionAsync(target, token);
            return;
        }

        agent.isStopped = false;
        agent.speed = moveSpeed * 1.2f; // 跟随时稍快
        agent.SetDestination(target);

        float timeout = 15f; // 超时时间
        float timer = 0f;
        float lastDistance = float.MaxValue;
        int stuckCounter = 0; // 卡住计数器
        const float updateInterval = 0.1f; // 100ms更新间隔

        while (timer < timeout)
        {
            token.ThrowIfCancellationRequested();

            // 检查NavMeshAgent是否仍然有效
            if (agent == null || !agent.enabled)
            {
                Debug.LogWarning("NavMeshAgent在移动过程中被禁用");
                break;
            }

            // 检查是否到达目标
            if (!agent.pathPending && agent.remainingDistance <= agent.stoppingDistance)
            {
                break;
            }

            // 检查是否卡住（距离没有明显变化）
            float currentDistance = agent.remainingDistance;
            if (Mathf.Abs(currentDistance - lastDistance) < 0.1f)
            {
                stuckCounter++;
                if (stuckCounter > 20) // 连续20次检查都没有明显移动，认为卡住了
                {
                    Debug.LogWarning("NavMesh移动可能卡住，强制结束");
                    break;
                }
            }
            else
            {
                stuckCounter = 0; // 重置卡住计数器
            }

            lastDistance = currentDistance;
            timer += updateInterval; // 使用固定的时间间隔

            // 固定更新频率，每100ms检查一次
            await UniTask.Delay(100, DelayType.DeltaTime, PlayerLoopTiming.Update, token);
        }

        if (agent != null && agent.enabled)
        {
            agent.isStopped = true;
        }

        if (timer >= timeout)
        {
            Debug.LogWarning("NavMesh移动超时");
        }
    }

}