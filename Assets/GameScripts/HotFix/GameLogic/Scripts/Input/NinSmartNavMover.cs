using UnityEngine;
using UnityEngine.AI;
using System;
using Cysharp.Threading.Tasks;
using System.Threading;

public class NinSmartNavMover: MonoBehaviour
{
    [Header("移动参数")]
    public float moveSpeed = 3f;           // 基础速度
    public float closeDistance = 0.5f;     // 插值距离
    public float midDistance = 10f;        // NavMesh 距离
    public float teleportDistance = 30f;   // 超过这个距离瞬移
    public float stoppingDistance = 0.1f;  // 最终停止距离
    private NinChatacterTransfer _ninChatacterTransfer;
    private NavMeshAgent agent;
    private CancellationTokenSource moveCts;
    private Vector3 _currentTargetPosition = Vector3.zero;
    // private Vector3 _lastTargetPosition = Vector3.zero; // 记录最后一次的目标位置
    private bool isMoving = false;
    public bool IsMoving => isMoving;

    // public Action OnArrived; // ✅ 到达目标的回调

    // private enum MoveMode { None, Lerp, NavMesh, Teleport }
    // private MoveMode currentMode = MoveMode.None;

    // 回调
    public Action OnArrivedAll; // 所有路径到达
    public Action<int> OnArrivedPoint; // 每个路径点到达，参数为索引
    // [Header("配置参数")]
    // private float closeDistance = 3f;        // 近距离插值平滑
    // private float midDistance = 7f;           // 中距离用NavMesh
    // private float farDistance = 50f;          // 超远瞬移
    // private float moveSpeed = 3f;           // 插值速度
    // private float _flowDistance = 1f;

    // private NavMeshAgent agent;
    // private Coroutine moveRoutine;
    // private bool isMoving = false;
    // private Vector3 _currentTargetPosition = Vector3.zero;

    void Awake()
    {
        _ninChatacterTransfer = this.gameObject.GetComponentInChildren<NinChatacterTransfer>();
        agent = GetComponent<NavMeshAgent>();
        if (agent == null)
        {
            agent = gameObject.AddComponent<NavMeshAgent>();
        }
        agent.speed = moveSpeed;
        agent.updateRotation = true;
    }

    void OnDestroy()
    {
        // 确保在对象销毁时取消移动任务
        StopMoving();
    }
    public void SetMoveSpeed(float speed) {
        moveSpeed = speed;
        agent.speed = moveSpeed;
    }
    public Vector3 GetMoveDirection() {
        if(isMoving) {
            return _currentTargetPosition - transform.position;
        }
        return Vector3.zero;
    }
    /// <summary>
    /// 开始沿路径移动
    /// </summary>
    // public void MoveAlongPath(Vector3[] points)
    // {
    //     StopMoving();

    //     if (points == null || points.Length == 0)
    //     {
    //         Debug.LogWarning("路径为空，无法移动");
    //         return;
    //     }

    //     moveRoutine = StartCoroutine(MoveRoutine(points));
    // }

    public void SetNewTargetLocs(MainServer.TrainerLoc[] positions)
    {
        // _targetPositions.AddRange(positions.ToList());
        // if (_targetPositions.Count > 0)
        // {
        //     var nextTarget = _targetPositions[0];
        //     _targetPositions.RemoveAt(0);
        //     SetNewTargetLoc(nextTarget);
        // }
    }
    public Vector3 GetCurrentTargetPosition() {
        return _currentTargetPosition;
        // return transform.forward;
    }
    /// <summary>
    /// 移动到单个点
    /// </summary>
    public void MoveToPosition(Vector3 position)
    {
        // 检查位置是否与上次相同
        if (Vector3.Distance(position, _currentTargetPosition) < 0.01f)
        {
            Debug.Log("目标位置与上次相同，跳过移动");
            return;
        }

        MoveAlongPath(new Vector3[] { position });
    }

    /// <summary>
    /// 按路径移动
    /// </summary>
    public void MoveAlongPath(Vector3[] pathPoints)
    {
        StopMoving();
        MoveRoutineAsync(pathPoints).Forget();
    }

    /// <summary>
    /// 停止移动
    /// </summary>
    public void StopMoving()
    {
        if (moveCts != null)
        {
            moveCts.Cancel();
            moveCts.Dispose();
            moveCts = null;
        }

        if (agent.enabled)
        {
            agent.isStopped = true;
        }

        isMoving = false;
    }

    /// <summary>
    /// 主移动任务：沿路径依次移动
    /// </summary>
    private async UniTask MoveRoutineAsync(Vector3[] points)
    {
        moveCts = new CancellationTokenSource();
        var token = moveCts.Token;

        try
        {
            isMoving = true;

            for (int i = 0; i < points.Length; i++)
            {
                Vector3 target = points[i];
                // 检查位置是否与上次相同
                if (Vector3.Distance(target, _currentTargetPosition) < 0.01f)
                {
                    _currentTargetPosition = target;
                    Debug.Log("目标位置与上次相同，跳过移动");
                    return;
                }
                _currentTargetPosition = target;
                // _lastTargetPosition = target; // 记录最后一次的目标位置

                while (true)
                {
                    token.ThrowIfCancellationRequested();

                    float distance = Vector3.Distance(transform.position, target);

                    if (distance <= stoppingDistance)
                    {
                        OnArrivedPoint?.Invoke(i);
                        break; // 继续下一个点
                    }

                    if (distance > closeDistance && distance < midDistance)
                    {
                        await LerpToPositionAsync(target, token);
                        break;
                    }
                    else if (distance >= midDistance && distance < teleportDistance)
                    {
                        await NavMeshMoveAsync(target, token);
                        break;
                    }
                    else if (distance >= teleportDistance)
                    {
                        await _ninChatacterTransfer.Transfer(target);
                        await UniTask.Yield(PlayerLoopTiming.Update, token); // 给一帧
                        break;
                    }

                    await UniTask.Yield(PlayerLoopTiming.Update, token);
                }
            }

            isMoving = false;
            OnArrivedAll?.Invoke();
        }
        catch (OperationCanceledException)
        {
            // 移动被取消
            isMoving = false;
        }
        finally
        {
            if (moveCts != null)
            {
                moveCts.Dispose();
                moveCts = null;
            }
        }
    }

    /// <summary>
    /// 插值平滑移动
    /// </summary>
    private async UniTask LerpToPositionAsync(Vector3 target, CancellationToken token)
    {
        while (Vector3.Distance(transform.position, target) > stoppingDistance)
        {
            token.ThrowIfCancellationRequested();
            transform.position = Vector3.MoveTowards(transform.position, target, moveSpeed * Time.deltaTime);
            await UniTask.Yield(PlayerLoopTiming.Update, token);
        }
        transform.position = target;
    }

    /// <summary>
    /// 使用 NavMeshAgent 移动
    /// </summary>
    private async UniTask NavMeshMoveAsync(Vector3 target, CancellationToken token)
    {
        agent.isStopped = false;
        agent.speed = moveSpeed * 1.2f; // 跟随时稍快
        agent.SetDestination(target);

        float timeout = 10f;
        float timer = 0f;

        while ((agent.pathPending || agent.remainingDistance > agent.stoppingDistance) && timer < timeout)
        {
            token.ThrowIfCancellationRequested();
            timer += Time.deltaTime;
            await UniTask.Yield(PlayerLoopTiming.Update, token);
        }

        agent.isStopped = true;
    }

}