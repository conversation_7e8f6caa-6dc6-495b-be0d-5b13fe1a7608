using UnityEngine;
using UnityEngine.AI;
using System;
using Cysharp.Threading.Tasks;
using System.Threading;

public class NinSmartNavMover: MonoBehaviour
{
    [Header("移动参数")]
    public float moveSpeed = 3f;           // 基础速度
    public float closeDistance = 3f;       // 近距离用插值（小于此距离）
    public float midDistance = 15f;        // 中距离用NavMesh（小于此距离）
    public float teleportDistance = 30f;   // 超过这个距离瞬移
    public float stoppingDistance = 0.1f;  // 最终停止距离
    private NinChatacterTransfer _ninChatacterTransfer;
    private NavMeshAgent agent;
    private CancellationTokenSource moveCts;
    private Vector3 _currentTargetPosition = Vector3.zero;
    // private Vector3 _lastTargetPosition = Vector3.zero; // 记录最后一次的目标位置
    private bool isMoving = false;
    public bool IsMoving => isMoving;

    // public Action OnArrived; // ✅ 到达目标的回调

    // private enum MoveMode { None, Lerp, NavMesh, Teleport }
    // private MoveMode currentMode = MoveMode.None;

    // 回调
    public Action OnArrivedAll; // 所有路径到达
    public Action<int> OnArrivedPoint; // 每个路径点到达，参数为索引
    // [Header("配置参数")]
    // private float closeDistance = 3f;        // 近距离插值平滑
    // private float midDistance = 7f;           // 中距离用NavMesh
    // private float farDistance = 50f;          // 超远瞬移
    // private float moveSpeed = 3f;           // 插值速度
    // private float _flowDistance = 1f;

    // private NavMeshAgent agent;
    // private Coroutine moveRoutine;
    // private bool isMoving = false;
    // private Vector3 _currentTargetPosition = Vector3.zero;

    void Awake()
    {
        _ninChatacterTransfer = this.gameObject.GetComponentInChildren<NinChatacterTransfer>();
        agent = GetComponent<NavMeshAgent>();
        if (agent == null)
        {
            agent = gameObject.AddComponent<NavMeshAgent>();
        }
        agent.speed = moveSpeed;
        agent.updateRotation = true;
    }

    void OnDestroy()
    {
        // 确保在对象销毁时取消移动任务
        StopMoving();
    }
    public void SetMoveSpeed(float speed) {
        moveSpeed = speed;
        agent.speed = moveSpeed;
    }
    public Vector3 GetMoveDirection() {
        if(isMoving) {
            return _currentTargetPosition - transform.position;
        }
        return Vector3.zero;
    }
    /// <summary>
    /// 开始沿路径移动
    /// </summary>
    // public void MoveAlongPath(Vector3[] points)
    // {
    //     StopMoving();

    //     if (points == null || points.Length == 0)
    //     {
    //         Debug.LogWarning("路径为空，无法移动");
    //         return;
    //     }

    //     moveRoutine = StartCoroutine(MoveRoutine(points));
    // }

    public void SetNewTargetLocs(MainServer.TrainerLoc[] positions)
    {
        // _targetPositions.AddRange(positions.ToList());
        // if (_targetPositions.Count > 0)
        // {
        //     var nextTarget = _targetPositions[0];
        //     _targetPositions.RemoveAt(0);
        //     SetNewTargetLoc(nextTarget);
        // }
    }
    public Vector3 GetCurrentTargetPosition() {
        return _currentTargetPosition;
        // return transform.forward;
    }

    /// <summary>
    /// Transfer完成后调用，同步NavMeshAgent状态
    /// </summary>
    public void OnTransferCompleted()
    {
        if (agent != null && agent.enabled)
        {
            agent.ResetPath();
            agent.isStopped = true;
            agent.Warp(transform.position);
        }
        // 不重置_currentTargetPosition，保持当前目标
    }
    /// <summary>
    /// 移动到单个点
    /// </summary>
    public void MoveToPosition(Vector3 position)
    {
        // 检查位置是否与上次相同
        if (Vector3.Distance(position, _currentTargetPosition) < 0.01f && isMoving)
        {
            Debug.Log("目标位置与上次相同且正在移动中，跳过移动");
            return;
        }

        MoveAlongPath(new Vector3[] { position });
    }

    /// <summary>
    /// 按路径移动
    /// </summary>
    public void MoveAlongPath(Vector3[] pathPoints)
    {
        StopMoving();
        MoveRoutineAsync(pathPoints).Forget();
    }

    /// <summary>
    /// 停止移动
    /// </summary>
    public void StopMoving()
    {
        if (moveCts != null)
        {
            moveCts.Cancel();
            moveCts.Dispose();
            moveCts = null;
        }

        if (agent != null && agent.enabled)
        {
            agent.isStopped = true;
            agent.ResetPath(); // 清除NavMeshAgent的路径
        }

        isMoving = false;
        _currentTargetPosition = Vector3.zero; // 重置当前目标位置
    }

    /// <summary>
    /// 主移动任务：沿路径依次移动
    /// </summary>
    private async UniTask MoveRoutineAsync(Vector3[] points)
    {
        moveCts = new CancellationTokenSource();
        var token = moveCts.Token;

        try
        {
            isMoving = true;

            for (int i = 0; i < points.Length; i++)
            {
                Vector3 target = points[i];
                _currentTargetPosition = target;

                while (true)
                {
                    token.ThrowIfCancellationRequested();

                    float distance = Vector3.Distance(transform.position, target);

                    if (distance <= stoppingDistance)
                    {
                        OnArrivedPoint?.Invoke(i);
                        break; // 继续下一个点
                    }

                    // 修正距离判断逻辑
                    if (distance <= closeDistance)
                    {
                        await LerpToPositionAsync(target, token);
                        break;
                    }
                    else if (distance <= midDistance)
                    {
                        await NavMeshMoveAsync(target, token);
                        break;
                    }
                    else if (distance > teleportDistance)
                    {
                        // Transfer后需要清除NavMeshAgent的路径，防止回到原位置
                        if (agent != null && agent.enabled)
                        {
                            agent.ResetPath();
                            agent.isStopped = true;
                        }

                        await _ninChatacterTransfer.Transfer(target);

                        // Transfer完成后，确保NavMeshAgent状态正确
                        await UniTask.Delay(100, DelayType.DeltaTime, PlayerLoopTiming.Update, token);
                        if (agent != null && agent.enabled)
                        {
                            agent.Warp(transform.position); // 确保NavMeshAgent位置同步
                        }
                        break;
                    }

                    // 降低主循环更新频率，每100ms检查一次
                    await UniTask.Delay(100, DelayType.DeltaTime, PlayerLoopTiming.Update, token);
                }
            }

            isMoving = false;
            OnArrivedAll?.Invoke();
        }
        catch (OperationCanceledException)
        {
            // 移动被取消
            isMoving = false;
        }
        finally
        {
            if (moveCts != null)
            {
                moveCts.Dispose();
                moveCts = null;
            }
        }
    }

    /// <summary>
    /// 插值平滑移动
    /// </summary>
    private async UniTask LerpToPositionAsync(Vector3 target, CancellationToken token)
    {
        float timeout = 10f; // 超时保护
        float timer = 0f;
        const float updateInterval = 0.05f; // 50ms更新间隔

        while (Vector3.Distance(transform.position, target) > stoppingDistance && timer < timeout)
        {
            token.ThrowIfCancellationRequested();

            // 使用固定的时间步长进行移动
            transform.position = Vector3.MoveTowards(transform.position, target, moveSpeed * updateInterval);
            timer += updateInterval;

            // 固定更新频率，每50ms更新一次
            await UniTask.Delay(50, DelayType.DeltaTime, PlayerLoopTiming.Update, token);
        }

        // 确保到达目标位置
        if (timer < timeout)
        {
            transform.position = target;
        }
        else
        {
            Debug.LogWarning("插值移动超时");
        }
    }

    /// <summary>
    /// 使用 NavMeshAgent 移动
    /// </summary>
    private async UniTask NavMeshMoveAsync(Vector3 target, CancellationToken token)
    {
        if (agent == null || !agent.enabled)
        {
            Debug.LogWarning("NavMeshAgent 不可用，切换到插值移动");
            await LerpToPositionAsync(target, token);
            return;
        }

        agent.isStopped = false;
        agent.speed = moveSpeed * 1.2f; // 跟随时稍快
        agent.SetDestination(target);

        float timeout = 15f; // 超时时间
        float timer = 0f;
        float lastDistance = float.MaxValue;
        int stuckCounter = 0; // 卡住计数器
        const float updateInterval = 0.1f; // 100ms更新间隔

        while (timer < timeout)
        {
            token.ThrowIfCancellationRequested();

            // 检查NavMeshAgent是否仍然有效
            if (agent == null || !agent.enabled)
            {
                Debug.LogWarning("NavMeshAgent在移动过程中被禁用");
                break;
            }

            // 检查是否到达目标
            if (!agent.pathPending && agent.remainingDistance <= agent.stoppingDistance)
            {
                break;
            }

            // 检查是否卡住（距离没有明显变化）
            float currentDistance = agent.remainingDistance;
            if (Mathf.Abs(currentDistance - lastDistance) < 0.1f)
            {
                stuckCounter++;
                if (stuckCounter > 20) // 连续20次检查都没有明显移动，认为卡住了
                {
                    Debug.LogWarning("NavMesh移动可能卡住，强制结束");
                    break;
                }
            }
            else
            {
                stuckCounter = 0; // 重置卡住计数器
            }

            lastDistance = currentDistance;
            timer += updateInterval; // 使用固定的时间间隔

            // 固定更新频率，每100ms检查一次
            await UniTask.Delay(100, DelayType.DeltaTime, PlayerLoopTiming.Update, token);
        }

        if (agent != null && agent.enabled)
        {
            agent.isStopped = true;
        }

        if (timer >= timeout)
        {
            Debug.LogWarning("NavMesh移动超时");
        }
    }

}