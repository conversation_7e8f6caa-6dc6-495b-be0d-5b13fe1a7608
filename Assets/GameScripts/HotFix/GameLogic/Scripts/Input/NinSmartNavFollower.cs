using UnityEngine;
using UnityEngine.AI;
using System.Collections;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;

public class NinSmartNavFollower : MonoBehaviour
{
    [Header("核心组件")]
    public NinSmartNavMover smartNavMover;

    // [Header("跟随配置")]
    public Transform leader { get; private set; }          // 主角
    private NinChatacterTransfer _ninChatacterTransfer;
    private bool _isPoke = true;
    private int slotIndex = 0;          // 插槽编号（0=最靠近）
    private float slotSpacing = 1f;   // 插槽间距
    private float updateRate = 0.2f;    // 更新频率（提高到0.1秒）
    private float closeDistance = 1f; // 停止移动判定（增大到1.2f）
    private float directionUpdateThreshold = 1.5f; // 主角走这么远才更新方向（减小到1.0f）
    private float interpolationSpeed = 8f;         // 插值平滑速度（提高到8f）

    // [Header("调试")]
    private bool showDebugGizmos = true;

    private Coroutine followRoutine;
    private Vector3 lastLeaderPos;
    private Vector3 lastLeaderDir; // 缓存的主角行进方向
    private Vector3 targetPos;     // 当前目标点
    private Vector3 lastTargetPos; // 上一次的目标点，用于避免频繁更新
    private float targetUpdateThreshold = 0.8f; // 目标点更新阈值

    public bool IsMoving => smartNavMover.IsMoving;
    public Vector3 GetMoveDirection()
    {
        return smartNavMover.GetMoveDirection();
    }
    void Awake()
    {
        _ninChatacterTransfer = this.gameObject.GetComponentInChildren<NinChatacterTransfer>();
    }
    // public bool IsMoving
    // {
    //     get
    //     {
    //         return followRoutine != null;
    //     }
    // }
    public void SetCloseDistance(float distance)
    {
        closeDistance = distance + 0.5f;
    }
    public async UniTask SetLocInfo(MainServer.TrainerLocInfo locInfo)
    {
        //链路地址
        if (locInfo == null)
        {
            return;
        }
        var locs = new List<MainServer.TrainerLoc>();
        MainServer.TrainerLoc? transformLoc = null;
        foreach (var loc in locInfo.LocLine)
        {
            if (loc.MainLandType != locInfo.Loc.MainLandType && loc.MapName != locInfo.Loc.MapName && _ninChatacterTransfer != null && _ninChatacterTransfer.ninCharacter.isMe)
            {
                transformLoc = loc;
                locs.Clear();
            }
            locs.Add(loc);
        }
        //其他角色可以不用考虑加载新地图，直接传送就行
        if(transformLoc != null && _ninChatacterTransfer != null && _ninChatacterTransfer.ninCharacter.isMe) {
            var position = new Vector3(transformLoc.X, transformLoc.Y, transformLoc.Z);
            await MapController.Current.transferMapMgr.MoveToNewMap(_ninChatacterTransfer, MainMapInfo.Create(transformLoc.MainLandType), position);
        }
        if (locs.Count == 0)
        {
            locs.Add(locInfo.Loc);
        }
        smartNavMover.SetNewTargetLocs(locs.ToArray());
    }

    
    public void ResetPath()
    {
        // StopFollow();
        smartNavMover.StopMoving();
    }

    void OnEnable()
    {
        if (leader != null)
        {
            StartFollow();
        }
    }

    public void SetLeader(Transform newLeader, int newSlotIndex)
    {
        if (newLeader == null)
        {
            StopFollow();
            return;
        }
        leader = newLeader;
        slotIndex = newSlotIndex;
        StartFollow();
    }

    public void SetMoveSpeed(float speed)
    {
        smartNavMover.SetMoveSpeed(speed);
    }

    public void StartFollow()
    {
        StopFollow();
        followRoutine = StartCoroutine(FollowLeader());
    }

    public void StopFollow()
    {
        if (followRoutine != null)
        {
            StopCoroutine(followRoutine);
            followRoutine = null;
        }
        smartNavMover.StopMoving();
    }

    private IEnumerator FollowLeader()
    {
        lastLeaderPos = leader.position;
        lastLeaderDir = leader.forward;

        while (true)
        {
            if (leader != null)
            {
                _isPoke = _ninChatacterTransfer.ninCharacter.ninCharacterType == NinCharacter.NinCharacterType.Poke;
                // 检查 leader 是否移动了足够距离
                float leaderMoved = Vector3.Distance(leader.position, lastLeaderPos);
                if (leaderMoved > directionUpdateThreshold)
                {
                    // 根据主角移动方向更新阵型方向
                    Vector3 moveDir = (leader.position - lastLeaderPos).normalized;
                    if (moveDir.sqrMagnitude > 0.01f)
                    {
                        lastLeaderDir = moveDir;
                    }
                    lastLeaderPos = leader.position;
                }
                float leaderDis = Vector3.Distance(leader.position, transform.position);
                if(leaderDis < closeDistance) {
                    yield return new WaitForSeconds(updateRate);
                    continue;
                }

                // 计算插槽位置
                Vector3 leaderRight = Vector3.Cross(Vector3.up, lastLeaderDir);
                int rowCount = _isPoke ? 2 : 3;
                int row = (slotIndex / rowCount) + 1;
                int side = (slotIndex % rowCount == 0) ? -1 : 1;
                if(!_isPoke) {
                    switch(slotIndex % rowCount) {
                        case 0:
                            side = -1;
                            break;
                        case 1:
                            side = 0;
                            break;
                        case 2:
                            side = 1;
                            break;
                    }
                }
                var finalSlotSpacing = _isPoke ? slotSpacing : slotSpacing * 1.5f;
                // int side = (slotIndex % rowCount == 0) ? -1 : 1; // 左右交替
                Vector3 offset = (lastLeaderDir * -finalSlotSpacing * row) + (leaderRight * side * finalSlotSpacing);

                // 目标位置（平滑插值）
                Vector3 desiredPos = leader.position + offset;
                // targetPos = targetPos == Vector3.zero ? transform.position : targetPos;
                targetPos = desiredPos;
                // targetPos = Vector3.Lerp(targetPos == Vector3.zero ? transform.position : targetPos, desiredPos, Time.deltaTime * interpolationSpeed);

                // 距离检查 - 只有在目标位置变化足够大时才更新移动
                float distanceToTarget = Vector3.Distance(transform.position, targetPos);
                float targetPosChange = Vector3.Distance(targetPos, lastTargetPos);

                if (distanceToTarget > closeDistance)
                {
                    // 只有当目标位置变化超过阈值时才重新设置移动目标
                    if (targetPosChange > targetUpdateThreshold || !smartNavMover.IsMoving)
                    {
                        smartNavMover.AddMoveToPosition(targetPos);
                        lastTargetPos = targetPos;
                    }
                }
                else
                {
                    smartNavMover.StopMoving();
                }
            }

            yield return new WaitForSeconds(updateRate);
        }
    }

    private void OnDrawGizmos()
    {
        if (showDebugGizmos && leader != null)
        {
            Gizmos.color = Color.green;
            Gizmos.DrawSphere(targetPos, 0.2f);
            Gizmos.DrawLine(transform.position, targetPos);
        }
    }
}