using Cysharp.Threading.Tasks;
using PokeApiNet;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using GameLogic.Scripts.Extension;

public class NinCharacterTopUI : MonoBehaviour
{
    public TMP_Text nameText;
    public Image iconPrefab;
    public LayoutGroup iconLayout;
    public Button clickBtn;
    // public NinChatacterOprationTip oprationTip;
    MainServer.Trainer _trainer;
    void Awake() {
        clickBtn.onClick.AddListener(OnClick);
    }
    void OnClick() {
        TrainerInfoUI.Create(_trainer, MapController.Current.mapMenuUI.transform);
    }
    public void SetTrainer(MainServer.Trainer trainer) {
        _trainer = trainer;
        nameText.text = trainer.Name;
        this.gameObject.SetActive(true);
    }
    public void SetPokeInfo(MainServer.TrainerFollowPokeInfo pokeInfo) {
        this.gameObject.SetActive(false);
        // ConfigPokeInfo(pokeInfo).Forget();
    }
    public void SetNpc(MainServer.NpcRoleConfig config) {
        this.gameObject.SetActive(true);
        nameText.text = config.NameLocalized();
    }
    private async UniTaskVoid ConfigPokeInfo(MainServer.TrainerFollowPokeInfo pokeInfo) {
        //后续是不是要给精灵特别的icon
        // _baseLocalPoke = await PokeDataLoad.Share.GetIndexData<Pokemon>(name);
        // var localPokemonData = await PokeDataLoad.Share.GetIndexData<Pokemon>(pokeInfo.Name);
        // if(string.IsNullOrEmpty(pokeInfo.nickname)) {
        //     nameText.text = pokeInfo.nickname; 
        // } else {
        //     nameText.text = localPokemonData.NameLocalized;
        // }
    }
}