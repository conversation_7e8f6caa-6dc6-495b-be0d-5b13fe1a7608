using Cysharp.Threading.Tasks;
using PokeApiNet;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class NinChatacterOprationTip : MonoBehaviour
{
    public TMP_Text titleText;
    public Image iconPrefab;
    public Button clickBtn;
    // private OprationType _oprationType;
    void Awake() {
        clickBtn.onClick.AddListener(OnClick);
    }
    void OnClick() {
        Debug.Log("OnClick clickBtn");
        // MapController.Current.mapMenuUI.mapChatacterOprationUI.Show(_oprationType);
        // TrainerInfoUI.Create(_trainer, MapController.Current.mapMenuUI.transform);
    }
    // public void ConfigType(OprationType opration) {
    //     _oprationType = opration;
    //     switch (opration)
    //     {
    //         case OprationType.Water:
    //             if(titleText != null) {
    //                 titleText.gameObject.SetActive(false);
    //             }
    //             this.gameObject.SetActive(false);
    //             // if(titleText != null) {
    //             //     titleText.text = "水中";
    //             //     titleText.gameObject.SetActive(true);
    //             // }
    //             // this.gameObject.SetActive(true);
    //             break;
    //         case OprationType.OnWaterFaceLand:
    //             if(titleText != null) {
    //                 titleText.text = "水中面向岸边";
    //                 titleText.gameObject.SetActive(true);
    //             }
    //             this.gameObject.SetActive(true);
    //             break;
    //         case OprationType.OnLandFaceWater:
    //             if(titleText != null) {
    //                 titleText.text = "岸边面向水面";
    //                 titleText.gameObject.SetActive(true);
    //             }
    //             this.gameObject.SetActive(true);
    //             break;
    //         case OprationType.None:
    //             if(titleText != null) {
    //                 titleText.gameObject.SetActive(false);
    //             }
    //             this.gameObject.SetActive(false);
    //             break;
    //     }
    // }
    // void ConfigTitle(string title) {

    // }
}